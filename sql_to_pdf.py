import os
import pandas as pd
import textwrap
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Spacer, Preformatted
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import mm
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
import datetime
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import locale
from ConnectDB import get_all_data

# Database connection is handled by ConnectDB.py

# Set locale to Polish for proper sorting of Polish characters
try:
    locale.setlocale(locale.LC_COLLATE, 'pl_PL.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_COLLATE, 'Polish_Poland.1250')
    except:
        # If Polish locale is not available, fall back to default
        pass

def format_currency(value):
    """Format a value as currency with 2 decimal places."""
    if pd.isna(value):
        return "0.00"
    try:
        return f"{float(value):.2f}"
    except (ValueError, TypeError):
        return str(value)

def wrap_text(text, max_length=20):
    """
    Wrap text to fit within a specified width using the textwrap module.

    Args:
        text (str): The text to wrap
        max_length (int): Maximum length of each line

    Returns:
        str: Text with newlines inserted to prevent overflow
    """
    if not text:
        return ""

    # Convert to string and trim any leading/trailing whitespace
    text = str(text).strip()

    # If text is already short enough, return it as is
    if len(text) <= max_length:
        return text

    # Use textwrap to wrap the text
    wrapped_lines = textwrap.wrap(text, width=max_length, break_long_words=True)
    return "\n".join(wrapped_lines)

def create_ascii_table(headers, data, widths, align, subheaders=None):
    """Create an ASCII text table with the given headers and data.

    Args:
        headers: List of column headers
        data: List of rows, where each row is a list of values
        widths: List of column widths (in characters)
        align: List of alignments ('left', 'right', 'center') for each column
        subheaders: Optional second row of headers

    Returns:
        String containing the ASCII table
    """
    # Widths and align must be provided
    if not widths or not align:
        raise ValueError("Both widths and align must be provided")

    # Create the table
    table = []

    # Only add headers if they are provided
    if headers:
        # Create the top border
        top_border = "┌"
        for width in widths:
            top_border += "─" * (width) + "┬"
        top_border = top_border[:-1] + "┐"

        # Create the header row
        header_row = "│"
        for i, header in enumerate(headers):
            if align[i] == 'right':
                header_row += f"{str(header):>{widths[i]}}│"
            elif align[i] == 'center':
                header_row += f"{str(header):^{widths[i]}}│"
            else:  # left alignment
                header_row += f"{str(header):<{widths[i]}}│"

        # Add headers to table
        table.append(top_border)
        table.append(header_row)

        # Add subheaders if provided
        if subheaders:
            subheader_row = "│"
            for i, subheader in enumerate(subheaders):
                if i < len(widths):
                    if align[i] == 'right':
                        subheader_row += f"{str(subheader):>{widths[i]}}│"
                    elif align[i] == 'center':
                        subheader_row += f"{str(subheader):^{widths[i]}}│"
                    else:  # left alignment
                        subheader_row += f"{str(subheader):<{widths[i]}}│"
            table.append(subheader_row)

        # Create the separator row
        separator = "├"
        for width in widths:
            separator += "─" * (width) + "┼"
        separator = separator[:-1] + "┤"

        # Add separator after headers
        table.append(separator)
    else:
        # For continuous tables (no headers), just add a top border
        # Use solid vertical bars without spaces between them
        # We don't actually need a top border for continuous tables
        # as they connect seamlessly with the previous table
        # Just add an empty string to maintain the table structure
        table.append("")

    # Add data rows
    for row_idx, row in enumerate(data):
        # Only preserve vertical spacing in EL. WYPLATY, OPODAT, NIEOPODAT columns
        multiline_columns = [2, 3, 4]
        has_multiline_in_special_cols = False

        for col_idx in multiline_columns:
            if col_idx < len(row) and row[col_idx] and "\n" in str(row[col_idx]):
                has_multiline_in_special_cols = True
                break

        if not has_multiline_in_special_cols:
            data_row = "│"
            for i, value in enumerate(row):
                if i < len(widths):
                    # Ensure value doesn't exceed cell width by truncating if necessary
                    safe_value = str(value)
                    if len(safe_value) > widths[i] - 2:
                        safe_value = safe_value[:widths[i] - 5] + "..."

                    if align[i] == 'right':
                        data_row += f"{safe_value:>{widths[i]}}│"
                    elif align[i] == 'center':
                        data_row += f"{safe_value:^{widths[i]}}│"
                    else:
                        data_row += f"{safe_value:<{widths[i]}}│"
            table.append(data_row)
        else:
            # Handle multiline content with reduced line spacing
            cell_lines = []
            max_lines = 1

            for i, value in enumerate(row):
                if i < len(widths):
                    if i in multiline_columns and value and "\n" in str(value):
                        lines = str(value).split("\n")
                        cell_lines.append(lines)
                        max_lines = max(max_lines, len(lines))
                    else:
                        cell_lines.append([str(value)])

            for i in range(len(cell_lines)):
                if i in multiline_columns:
                    while len(cell_lines[i]) < max_lines:
                        cell_lines[i].append("")

            for line_idx in range(max_lines):
                data_row = "│"
                for i, lines in enumerate(cell_lines):
                    if i < len(widths):
                        if i in multiline_columns:
                            value = lines[line_idx] if line_idx < len(lines) else ""
                        else:
                            value = lines[0] if line_idx == 0 else ""

                        # Ensure value doesn't exceed cell width by truncating if necessary
                        safe_value = str(value)
                        if len(safe_value) > widths[i] - 2:
                            safe_value = safe_value[:widths[i] - 5] + "..."

                        if align[i] == 'right':
                            data_row += f"{safe_value:>{widths[i]}}│"
                        elif align[i] == 'center':
                            data_row += f"{safe_value:^{widths[i]}}│"
                        else:
                            data_row += f"{safe_value:<{widths[i]}}│"
                table.append(data_row)

    # We don't add a bottom border for continuous tables
    # This allows tables to connect seamlessly

    return "\n".join(table)

def calculate_summary_data(df):
    """
    Calculate summary data from all employees in the DataFrame using pandas aggregation.

    Args:
        df (DataFrame): The DataFrame containing the payroll data

    Returns:
        dict: A dictionary containing the summary data
    """
    # Group data by employee to avoid double counting
    employees = {}
    for _, row in df.iterrows():
        employee_key = f"{row['PRI_Nazwisko']} {row['PRI_Imie1']}"
        if employee_key not in employees:
            employees[employee_key] = []
        employees[employee_key].append(row)

    # Initialize summary dictionary
    summary = {
        # Wartość fields
        'wartosc_opodat': 0,
        'wartosc_nieopodat': 0,

        # Ubezpieczenia fields
        'ubezpiecz_e': 0,  # Emerytalna
        'ubezpiecz_r': 0,  # Rentowa
        'ubezpiecz_c': 0,  # Chorobowa

        # PPK fields
        'ppk_p': 0,  # Pracownik 1
        'ppk_d': 0,  # Pracownik 2
        'ppk_p_platnik': 0,  # Pracodawca 1
        'ppk_d_platnik': 0,  # Pracodawca 2

        # Płatnik fields
        'platnik_e': 0,  # Emerytalna
        'platnik_r': 0,  # Rentowa
        'platnik_w': 0,  # Wypadkowa
        'platnik_fp': 0,  # Fundusz Pracy
        'platnik_fgsp': 0,  # Fundusz Gwarantowanych Świadczeń Pracowniczych
        'platnik_fep': 0,  # Fundusz Emerytur Pomostowych

        # Podatek fields
        'podatek_zal': 0,  # Zaliczka
        'podatek_zdr': 0,  # Zdrowotna
        'podatek_k': 0,    # Koszty
        'podatek_ulga': 0, # Ulga

        # Do wypłaty fields
        'do_wyplaty_got': 0,  # Gotówka - TODO: Update database query to include GOT payment information
        'do_wyplaty_ror': 0,  # Przelew (WPL_Wartosc)
    }

    # Process each employee
    for _, employee_data in employees.items():
        # Convert employee data to DataFrame for easier aggregation
        emp_df = pd.DataFrame(employee_data)

        # For ZUS contributions, we need to be careful about double counting
        # ZUS contributions are per employee, not per payment element
        # So we should take the first non-zero value or use a different aggregation strategy

        # Get unique ZUS contribution values for this employee
        # ZUS contributions should be the same across all rows for the same employee
        # We'll take the first non-null, non-zero value for each ZUS field

        zus_rows = emp_df[emp_df['TWP_SklZUS'] == 2]
        if not zus_rows.empty:
            # Take the first row with TWP_SklZUS = 2 for ZUS contributions
            zus_row = zus_rows.iloc[0]
            summary['ubezpiecz_e'] += float(zus_row.get('WPE_SklEmerPrac', 0) or 0)
            summary['ubezpiecz_r'] += float(zus_row.get('WPE_SklRentPrac', 0) or 0)
            summary['ubezpiecz_c'] += float(zus_row.get('WPE_SklChorPrac', 0) or 0)

            summary['platnik_e'] += float(zus_row.get('WPE_SklEmerFirma', 0) or 0)
            summary['platnik_r'] += float(zus_row.get('WPE_SklRentFirma', 0) or 0)
            summary['platnik_w'] += float(zus_row.get('WPE_SklWypadFirma', 0) or 0)
            summary['platnik_fp'] += float(zus_row.get('WPE_SklFP', 0) or 0)
            summary['platnik_fgsp'] += float(zus_row.get('WPE_SklFGSP', 0) or 0)

        # For FEP, take the first non-zero value
        fep_values = emp_df['WPE_PodstFEP'].fillna(0).astype(float)
        fep_nonzero = fep_values[fep_values != 0]
        if not fep_nonzero.empty:
            summary['platnik_fep'] += fep_nonzero.iloc[0]

        # For PPK values, take the first non-zero value for each field
        ppk_p_values = emp_df['WPE_SklPPKPrac1'].fillna(0).astype(float)
        ppk_p_nonzero = ppk_p_values[ppk_p_values != 0]
        if not ppk_p_nonzero.empty:
            summary['ppk_p'] += ppk_p_nonzero.iloc[0]

        ppk_d_values = emp_df['WPE_SklPPKPrac2'].fillna(0).astype(float)
        ppk_d_nonzero = ppk_d_values[ppk_d_values != 0]
        if not ppk_d_nonzero.empty:
            summary['ppk_d'] += ppk_d_nonzero.iloc[0]

        ppk_p_platnik_values = emp_df['WPE_SklPPKFirma1'].fillna(0).astype(float)
        ppk_p_platnik_nonzero = ppk_p_platnik_values[ppk_p_platnik_values != 0]
        if not ppk_p_platnik_nonzero.empty:
            summary['ppk_p_platnik'] += ppk_p_platnik_nonzero.iloc[0]

        ppk_d_platnik_values = emp_df['WPE_SklPPKFirma2'].fillna(0).astype(float)
        ppk_d_platnik_nonzero = ppk_d_platnik_values[ppk_d_platnik_values != 0]
        if not ppk_d_platnik_nonzero.empty:
            summary['ppk_d_platnik'] += ppk_d_platnik_nonzero.iloc[0]

        # For tax-related fields, take the first non-zero value
        tax_zal_values = emp_df['WPE_ZalFis'].fillna(0).astype(float)
        tax_zal_nonzero = tax_zal_values[tax_zal_values != 0]
        if not tax_zal_nonzero.empty:
            summary['podatek_zal'] += tax_zal_nonzero.iloc[0]

        tax_zdr_values = emp_df['WPE_SklZdrowPrac'].fillna(0).astype(float)
        tax_zdr_nonzero = tax_zdr_values[tax_zdr_values != 0]
        if not tax_zdr_nonzero.empty:
            summary['podatek_zdr'] += tax_zdr_nonzero.iloc[0]

        tax_costs_values = emp_df['WPE_Koszty'].fillna(0).astype(float)
        tax_costs_nonzero = tax_costs_values[tax_costs_values != 0]
        if not tax_costs_nonzero.empty:
            summary['podatek_k'] += tax_costs_nonzero.iloc[0]

        tax_ulga_values = emp_df['WPE_Ulga'].fillna(0).astype(float)
        tax_ulga_nonzero = tax_ulga_values[tax_ulga_values != 0]
        if not tax_ulga_nonzero.empty:
            summary['podatek_ulga'] += tax_ulga_nonzero.iloc[0]

        # TODO: Update database query to include GOT payment information
        # For now, GOT is always 0 and ROR uses WPL_Wartosc
        summary['do_wyplaty_got'] += 0  # Always 0 until database query is updated
        summary['do_wyplaty_ror'] += float(employee_data[0].get('WPL_Wartosc', 0) or 0)  # Use the first value

        # Calculate wartość opodat and nieopodat based on TWP_RodzajFIS
        for _, row in emp_df.iterrows():
            wpe_wartosc = row.get('WPE_Wartosc', 0)
            if pd.isna(wpe_wartosc):
                continue

            # Convert to float to ensure proper calculation
            wpe_wartosc = float(wpe_wartosc)

            # Check TWP_RodzajFIS to determine if OPODAT or NIEOPODAT
            # TWP_RodzajFIS=1 indicates NIEOPODAT, any other value indicates OPODAT
            twp_rodzaj_fis = row.get('TWP_RodzajFIS', 0)

            if twp_rodzaj_fis == 1:
                # If TWP_RodzajFIS=1, add to nieopodat
                summary['wartosc_nieopodat'] += wpe_wartosc
            else:
                # Otherwise, add to opodat
                summary['wartosc_opodat'] += wpe_wartosc

    return summary

def create_summary_table(summary_data):
    """
    Create an ASCII table with the summary data.

    Args:
        summary_data (dict): The summary data dictionary

    Returns:
        str: The ASCII table as a string
    """
    # Format all values as currency
    for key in summary_data:
        summary_data[key] = format_currency(summary_data[key])

    # Use the same column structure as the main table but start from WARTOSC column
    # Main table headers: ["LP", "PRACOWNIK", "EL. WYPŁATY", "WARTOŚĆ", "", "SKŁADKI", "", "PODATEK", "DO WYPŁATY"]
    # Main table subheaders: ["", "", "", "OPODAT", "NIEOPODAT", "UBEZPIECZ", "PŁATNIK", "", ""]
    # Main table widths: [4, 22, 42, 12, 12, 16, 16, 16, 18]
    # Main table align: ['center', 'left', 'left', 'right', 'right', 'left', 'left', 'left', 'right']

    # Create headers for the summary table - matching main table structure but with empty LP, PRACOWNIK, EL. WYPŁATY
    headers = ["", "", "", "WARTOŚĆ", "", "SKŁADKI", "", "PODATEK", "DO WYPŁATY"]
    subheaders = ["", "", "", "OPODAT", "NIEOPODAT", "UBEZPIECZ", "PŁATNIK", "", ""]

    # Create data rows - adding empty columns for LP, PRACOWNIK, EL. WYPŁATY
    # Calculate totals for each column
    ubezpiecz_total = float(summary_data['ubezpiecz_e'].replace(',', '')) + \
                      float(summary_data['ubezpiecz_r'].replace(',', '')) + \
                      float(summary_data['ubezpiecz_c'].replace(',', ''))

    platnik_total = float(summary_data['platnik_e'].replace(',', '')) + \
                    float(summary_data['platnik_r'].replace(',', '')) + \
                    float(summary_data['platnik_w'].replace(',', '')) + \
                    float(summary_data['platnik_fp'].replace(',', '')) + \
                    float(summary_data['platnik_fgsp'].replace(',', '')) + \
                    float(summary_data['platnik_fep'].replace(',', ''))

    do_wyplaty_total = float(summary_data['do_wyplaty_got'].replace(',', '')) + \
                       float(summary_data['do_wyplaty_ror'].replace(',', ''))

    data = [
        # Row 1: E values
        [
            "", "", "",
            format_currency(float(summary_data['wartosc_opodat'].replace(',', ''))),
            format_currency(float(summary_data['wartosc_nieopodat'].replace(',', ''))),
            f"E: {summary_data['ubezpiecz_e']}",
            f"E: {summary_data['platnik_e']}",
            f"Zal: {summary_data['podatek_zal']}",
            f"KWOTA: {format_currency(do_wyplaty_total)}"
        ],
        # Row 2: R values
        [
            "", "", "",
            "",
            "",
            f"R: {summary_data['ubezpiecz_r']}",
            f"R: {summary_data['platnik_r']}",
            f"Zdr: {summary_data['podatek_zdr']}",
            ""
        ],
        # Row 3: C/W/K values
        [
            "", "", "",
            "",
            "",
            f"C: {summary_data['ubezpiecz_c']}",
            f"W: {summary_data['platnik_w']}",
            f"K: {summary_data['podatek_k']}",
            ""
        ],
        # Row 4: FP/Ulga values
        [
            "", "", "",
            "",
            "",
            "",
            f"FP: {summary_data['platnik_fp']}",
            f"Ulga: {summary_data['podatek_ulga']}",
            ""
        ],
        # Row 5: FGŚP values
        [
            "", "", "",
            "",
            "",
            "",
            f"FGŚP: {summary_data['platnik_fgsp']}",
            "",
            ""
        ],
        # Row 6: FEP values
        [
            "", "", "",
            "",
            "",
            "",
            f"FEP: {summary_data['platnik_fep']}",
            "",
            ""
        ],
        # Row 7: Totals row
        [
            "", "", "",
            "",
            "",
            f"Sum: {format_currency(ubezpiecz_total)}",
            f"Sum: {format_currency(platnik_total)}",
            ""
        ]
    ]

    # Add a separator before PPK section
    data.append([
        "", "", "",
        "",
        "",
        "────────────",
        "────────────",
        "",
        ""
    ])

    # Add PPK rows after the separator - Both PPK P and PPK D in both UBEZPIECZ and PLATNIK columns
    data.append([
        "", "", "",
        "",
        "",
        f"PPK P: {summary_data['ppk_p']}",
        f"PPK P: {summary_data['ppk_p_platnik']}",
        "",
        ""
    ])

    # Add second PPK row for PPK D values
    data.append([
        "", "", "",
        "",
        "",
        f"PPK D: {summary_data['ppk_d']}",
        f"PPK D: {summary_data['ppk_d_platnik']}",
        "",
        ""
    ])

    # Calculate PPK totals for each column
    ppk_ubezpiecz_total = float(summary_data['ppk_p'].replace(',', '')) + \
                          float(summary_data['ppk_d'].replace(',', ''))

    ppk_platnik_total = float(summary_data['ppk_p_platnik'].replace(',', '')) + \
                        float(summary_data['ppk_d_platnik'].replace(',', ''))

    # Add PPK total row
    data.append([
        "", "", "",
        "",
        "",
        f"R: {format_currency(ppk_ubezpiecz_total)}",
        f"R: {format_currency(ppk_platnik_total)}",
        "",
        ""
    ])

    # Use the same column widths and alignments as the main table
    widths = [4, 22, 42, 12, 12, 16, 16, 16, 18]  # Match main table widths
    align = ['center', 'left', 'left', 'right', 'right', 'left', 'left', 'left', 'right']  # Match main table alignments

    # Create the ASCII table
    table = create_ascii_table(headers, data, widths, align, subheaders)

    # Add a bottom border to the summary table
    bottom_border = "└"
    for width in widths:
        bottom_border += "─" * (width) + "┴"
    bottom_border = bottom_border[:-1] + "┘"

    return table + "\n" + bottom_border

def register_fonts():
    """Register fonts that support Polish characters."""
    font_path = "DejaVuSans.ttf"
    bold_font_path = "DejaVuSans-Bold.ttf"
    mono_font_path = "DejaVuSansMono.ttf"

    if not os.path.exists(font_path):
        possible_paths = [
            "C:/Windows/Fonts/DejaVuSans.ttf",
            "C:/Windows/Fonts/dejavu-sans/DejaVuSans.ttf",
            "C:/Windows/Fonts/arial.ttf"
        ]

        for path in possible_paths:
            if os.path.exists(path):
                font_path = path
                break

    if not os.path.exists(bold_font_path):
        possible_bold_paths = [
            "C:/Windows/Fonts/DejaVuSans-Bold.ttf",
            "C:/Windows/Fonts/dejavu-sans/DejaVuSans-Bold.ttf",
            "C:/Windows/Fonts/arialbd.ttf"
        ]

        for path in possible_bold_paths:
            if os.path.exists(path):
                bold_font_path = path
                break

    if not os.path.exists(mono_font_path):
        possible_mono_paths = [
            "C:/Windows/Fonts/DejaVuSansMono.ttf",
            "C:/Windows/Fonts/dejavu-sans-mono/DejaVuSansMono.ttf",
            "C:/Windows/Fonts/consola.ttf",  # Consolas is a good monospace font with Polish support
            "C:/Windows/Fonts/cour.ttf"      # Courier New also has decent Unicode support
        ]

        for path in possible_mono_paths:
            if os.path.exists(path):
                mono_font_path = path
                break

    try:
        pdfmetrics.registerFont(TTFont('DejaVuSans', font_path))
        pdfmetrics.registerFont(TTFont('DejaVuSans-Bold', bold_font_path))
        pdfmetrics.registerFont(TTFont('DejaVuSansMono', mono_font_path))
        return 'DejaVuSans', 'DejaVuSans-Bold'
    except Exception as e:
        print(f"Warning: Could not register custom fonts: {e}")
        print("Falling back to default fonts. Polish characters may not display correctly.")
        return 'Helvetica', 'Helvetica-Bold'

def create_styles(base_font, bold_font):
    """Create text styles for the PDF."""
    styles = getSampleStyleSheet()

    title_style = ParagraphStyle(
        'TitleStyle',
        parent=styles['Heading1'],
        fontName=bold_font,
        fontSize=12,
        alignment=TA_LEFT,
        spaceAfter=1*mm,
        encoding='utf-8'
    )

    subtitle_style = ParagraphStyle(
        'SubtitleStyle',
        parent=styles['Heading2'],
        fontName=bold_font,
        fontSize=10,
        alignment=TA_LEFT,
        spaceAfter=1*mm,
        encoding='utf-8'
    )

    header_style = ParagraphStyle(
        'HeaderStyle',
        parent=styles['Heading3'],
        fontName=bold_font,
        fontSize=7,
        alignment=TA_CENTER,
        spaceAfter=0,
        encoding='utf-8'
    )

    normal_style = ParagraphStyle(
        'NormalStyle',
        parent=styles['Normal'],
        fontName=base_font,
        fontSize=7,
        spaceAfter=0,
        encoding='utf-8'
    )

    small_style = ParagraphStyle(
        'SmallStyle',
        parent=styles['Normal'],
        fontName=base_font,
        fontSize=6,
        spaceAfter=0,
        encoding='utf-8'
    )

    right_style = ParagraphStyle(
        'RightStyle',
        parent=styles['Normal'],
        fontName=base_font,
        fontSize=7,
        alignment=TA_RIGHT,
        spaceAfter=0,
        encoding='utf-8'
    )

    center_style = ParagraphStyle(
        'CenterStyle',
        parent=styles['Normal'],
        fontName=base_font,
        fontSize=7,
        alignment=TA_CENTER,
        spaceAfter=0,
        encoding='utf-8'
    )

    # Add a monospace style for ASCII tables
    monospace_style = ParagraphStyle(
        'MonospaceStyle',
        parent=styles['Code'],
        fontName='DejaVuSansMono',  # Use DejaVuSansMono for better Polish character support
        fontSize=7,
        spaceAfter=0,
        encoding='utf-8'
    )

    return {
        'title': title_style,
        'subtitle': subtitle_style,
        'header': header_style,
        'normal': normal_style,
        'small': small_style,
        'right': right_style,
        'center': center_style,
        'monospace': monospace_style
    }

class FirstPageSpacer(Spacer):
    """A spacer that only adds space on the first page."""
    def __init__(self, width, height):
        Spacer.__init__(self, width, height)

    def wrap(self, availWidth, availHeight):
        # Only add space on the first page (page number 1)
        frame = self._doctemplateAttr('frame')
        if hasattr(frame, '_pagenum') and frame._pagenum > 0:
            # For pages beyond the first, return zero height
            return (availWidth, 0)
        else:
            # For the first page, return the normal height
            return Spacer.wrap(self, availWidth, availHeight)

def generate_payroll_pdf(df, output_file=None, list_number=None, return_data=False):
    """
    Generate a payroll PDF report from the DataFrame.

    Args:
        df (DataFrame): The DataFrame containing the payroll data
        output_file (str, optional): The output file path. If None, no file is saved.
        list_number (str, optional): The list number to display in the PDF
        return_data (bool, optional): If True, return the PDF data as bytes

    Returns:
        If return_data is True, returns bytes containing the PDF data
        Otherwise, returns the output_file path if successful, None otherwise
    """
    if df.empty:
        print("No data to generate PDF.")
        return None

    # Register fonts and create styles
    base_font, bold_font = register_fonts()
    styles = create_styles(base_font, bold_font)

    # Group data by employee
    employees = {}
    for _, row in df.iterrows():
        employee_key = f"{row['PRI_Nazwisko']} {row['PRI_Imie1']}"
        if employee_key not in employees:
            employees[employee_key] = []
        employees[employee_key].append(row)

    # Calculate summary data for all employees
    summary_data = calculate_summary_data(df)

    # Get period dates from first row
    if len(df) > 0:
        period_start = df['LPL_DataOd'].iloc[0] if not pd.isna(df['LPL_DataOd'].iloc[0]) else "N/A"
        period_end = df['LPL_DataDo'].iloc[0] if not pd.isna(df['LPL_DataDo'].iloc[0]) else "N/A"

        # Format payment date
        if not pd.isna(df['LPL_DataDok'].iloc[0]):
            try:
                payment_date_value = pd.to_datetime(df['LPL_DataDok'].iloc[0])
                payment_date = payment_date_value.strftime('%Y-%m-%d')
            except:
                payment_date = df['LPL_DataDok'].iloc[0]
        else:
            payment_date = "N/A"

        # Firma i NIP
        nazwa = df['Nazwa'].iloc[0] if 'Nazwa' in df.columns else "Brak nazwy"
        nip = f"NIP: {df['Nip'].iloc[0]}" if 'Nip' in df.columns else "Brak NIP"

        # Get LPL_Nazwa for the description
        lista_plac_nazwa = df['LPL_Nazwa'].iloc[0] if 'LPL_Nazwa' in df.columns and not pd.isna(df['LPL_Nazwa'].iloc[0]) else ""
    else:
        period_start = period_end = payment_date = nazwa = nip = "N/A"

    # If we're returning data, use a BytesIO buffer instead of a file
    if return_data:
        from io import BytesIO
        buffer = BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=12*mm,
            leftMargin=5*mm,
            topMargin=36*mm,  # Reduced top margin due to shorter header
            bottomMargin=15*mm
        )
    else:
        # Create PDF document with appropriate margins
        if output_file is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"lista_plac_{timestamp}.pdf"

        doc = SimpleDocTemplate(
            output_file,
            pagesize=A4,
            rightMargin=12*mm,
            leftMargin=5*mm,
            topMargin=36*mm,  # Reduced top margin due to shorter header
            bottomMargin=15*mm
        )

    # Define page header functions
    def first_page_header(canvas, doc):
        canvas.saveState()

        company_name = nazwa
        company_name2 = ""
        company_nip = nip
        doc_date = datetime.datetime.now().strftime("%Y-%m-%d")

        canvas.setFont('DejaVuSans-Bold', 9)
        canvas.drawString(doc.leftMargin, doc.height + doc.topMargin - 10*mm, company_name)
        canvas.drawString(doc.leftMargin, doc.height + doc.topMargin - 14*mm, company_name2)

        canvas.setFont('DejaVuSans-Bold', 7)
        canvas.drawString(doc.leftMargin, doc.height + doc.topMargin - 18*mm, company_nip)
        canvas.setFont('DejaVuSans', 7)
        canvas.drawRightString(doc.width + doc.leftMargin, doc.height + doc.topMargin - 10*mm, f"Data wydruku: {doc_date}")

        canvas.setFont('DejaVuSans-Bold', 12)
        canvas.drawString(doc.leftMargin, doc.height + doc.topMargin - 26*mm, "SZCZEGÓŁOWA LISTA PŁAC")

        # Add description from LPL_Nazwa if available
        if lista_plac_nazwa:
            canvas.setFont('DejaVuSans', 9)
            canvas.drawString(doc.leftMargin, doc.height + doc.topMargin - 30*mm, lista_plac_nazwa)

        current_page = canvas._pageNumber
        canvas.setFont('DejaVuSans', 7)

        # Position for the metadata header
        info_y = doc.height + doc.topMargin - 34*mm
        col1_x = doc.leftMargin + doc.width * 0.25
        col2_x = doc.leftMargin + doc.width * 0.55
        col3_x = doc.leftMargin + doc.width * 0.85

        # Draw the metadata header with right alignment
        canvas.drawRightString(col1_x, info_y, f"Numer: {list_number}")
        canvas.drawRightString(col2_x, info_y, f"Za okres: {period_start} - {period_end}")
        canvas.drawRightString(col3_x, info_y, f"Data wypłaty: {payment_date}")
        canvas.drawRightString(doc.width + doc.leftMargin, info_y, f"Strona: {current_page}")

        # Add a new line after the metadata header

        # Add vertical space after the header by increasing the FirstPageSpacer height
        # This is handled by adjusting the FirstPageSpacer in the content

        canvas.restoreState()

    # Simplified header for subsequent pages
    def later_page_header(canvas, doc):
        canvas.saveState()

        current_page = canvas._pageNumber
        canvas.setFont('DejaVuSans', 7)

        # Further reduce the vertical padding at the top of subsequent pages
        info_y = doc.height + doc.topMargin - 7*mm
        col1_x = doc.leftMargin + doc.width * 0.25
        col2_x = doc.leftMargin + doc.width * 0.55
        col3_x = doc.leftMargin + doc.width * 0.85

        # Draw the metadata header with right alignment
        canvas.drawRightString(col1_x, info_y, f"Numer: {list_number}")
        canvas.drawRightString(col2_x, info_y, f"Za okres: {period_start} - {period_end}")
        canvas.drawRightString(col3_x, info_y, f"Data wypłaty: {payment_date}")
        canvas.drawRightString(doc.width + doc.leftMargin, info_y, f"Strona: {current_page}")

        # Add vertical space after the header
        # This is handled by adjusting the translation in the later_pages function

        canvas.restoreState()

    # Add legend to the last page
    def add_legend(canvas, doc):
        canvas.saveState()

        legend_text = "E-Emerytalna; R-Rentowa; C-Chorobowa; W-Wypadkowa; FP-Fundusz Pracy; FGŚP-Fundusz Gwarantowanych Świadczeń Pracowniczych; FEP-Fundusz Emerytur Pomostowych; PPK P-Pracownicze Plany Kapitałowe pracownika; PPK D-PPK pracodawcy; Zal-Zaliczka na podatek; Zdr-Składka zdrowotna; K-Koszty uzyskania przychodu; Ulga-Ulga podatkowa; KWOTA-Suma wypłaty"

        canvas.setFont('DejaVuSans', 5)
        available_width = doc.width
        text_obj = canvas.beginText(doc.leftMargin, doc.bottomMargin - 5*mm)
        text_obj.setFont('DejaVuSans', 5)

        words = legend_text.split()
        line = ""
        for word in words:
            test_line = line + " " + word if line else word
            if canvas.stringWidth(test_line, 'DejaVuSans', 5) < available_width:
                line = test_line
            else:
                text_obj.textLine(line)
                line = word

        if line:
            text_obj.textLine(line)

        canvas.drawText(text_obj)
        canvas.restoreState()

    # Page callbacks
    def later_pages(canvas, doc):
        # For pages beyond the first, we'll significantly reduce the effective top margin
        # but still leave space after the metadata header
        canvas.translate(0, 10*mm)  # Move content up by 10mm on subsequent pages (less than before to add space after header)
        later_page_header(canvas, doc)
        add_legend(canvas, doc)

    def first_page(canvas, doc):
        first_page_header(canvas, doc)
        add_legend(canvas, doc)

    # Content for the PDF
    content = []

    # Add spacer at the top to account for the header on the first page only
    # Increased height to add vertical space after the metadata header
    content.append(FirstPageSpacer(1, 20*mm))

    # Process each employee individually
    employee_list = list(employees.items())
    # Sort employees by surname using Polish locale
    employee_list.sort(key=lambda x: locale.strxfrm(x[0].split()[0]))

    # Create employee tables
    for idx, (_, employee_data) in enumerate(employee_list):
        # Extract employee data
        emp_row = employee_data[0]  # Get first row for employee's basic info
        last_name = emp_row.get('PRI_Nazwisko', '')
        first_name = emp_row.get('PRI_Imie1', '')
        gross_salary = emp_row.get('PRE_ETAStawka', 0)
        employee_code = emp_row.get('PRI_Kod', '')
        akronim = f"[{employee_code}]"

        # Define headers only once - they'll only be used for the first employee
        headers = ["LP", "PRACOWNIK", "EL. WYPŁATY", "WARTOŚĆ", "", "SKŁADKI", "", "PODATEK", "DO WYPŁATY"]
        subheaders = ["", "", "", "OPODAT", "NIEOPODAT", "UBEZPIECZ", "PŁATNIK", "", ""]

        # Sum all contribution values across all rows for this employee
        emp_df = pd.DataFrame(employee_data)

        # Initialize ZUS contribution variables
        pension_contrib_emp = 0
        disability_contrib_emp = 0
        sickness_contrib = 0
        pension_contrib_firm = 0
        disability_contrib_firm = 0
        accident_contrib_firm = 0
        fp_contrib = 0
        fgsp_contrib = 0

        # Handle ZUS contributions - avoid double counting by taking first non-zero value
        zus_rows = emp_df[emp_df['TWP_SklZUS'] == 2]
        if not zus_rows.empty:
            # Take the first row with TWP_SklZUS = 2 for ZUS contributions
            zus_row = zus_rows.iloc[0]
            pension_contrib_emp = float(zus_row.get('WPE_SklEmerPrac', 0) or 0)
            disability_contrib_emp = float(zus_row.get('WPE_SklRentPrac', 0) or 0)
            sickness_contrib = float(zus_row.get('WPE_SklChorPrac', 0) or 0)

            pension_contrib_firm = float(zus_row.get('WPE_SklEmerFirma', 0) or 0)
            disability_contrib_firm = float(zus_row.get('WPE_SklRentFirma', 0) or 0)
            accident_contrib_firm = float(zus_row.get('WPE_SklWypadFirma', 0) or 0)
            fp_contrib = float(zus_row.get('WPE_SklFP', 0) or 0)
            fgsp_contrib = float(zus_row.get('WPE_SklFGSP', 0) or 0)

        # For other fields, take the first non-zero value to avoid double counting
        health_contrib_values = emp_df['WPE_SklZdrowPrac'].fillna(0).astype(float)
        health_contrib_nonzero = health_contrib_values[health_contrib_values != 0]
        health_contrib = health_contrib_nonzero.iloc[0] if not health_contrib_nonzero.empty else 0

        tax_advance_values = emp_df['WPE_ZalFis'].fillna(0).astype(float)
        tax_advance_nonzero = tax_advance_values[tax_advance_values != 0]
        tax_advance = tax_advance_nonzero.iloc[0] if not tax_advance_nonzero.empty else 0

        tax_relief_values = emp_df['WPE_Ulga'].fillna(0).astype(float)
        tax_relief_nonzero = tax_relief_values[tax_relief_values != 0]
        tax_relief = tax_relief_nonzero.iloc[0] if not tax_relief_nonzero.empty else 0

        tax_costs_values = emp_df['WPE_Koszty'].fillna(0).astype(float)
        tax_costs_nonzero = tax_costs_values[tax_costs_values != 0]
        tax_costs = tax_costs_nonzero.iloc[0] if not tax_costs_nonzero.empty else 0

        fep_contrib_values = emp_df['WPE_PodstFEP'].fillna(0).astype(float)
        fep_contrib_nonzero = fep_contrib_values[fep_contrib_values != 0]
        fep_contrib = fep_contrib_nonzero.iloc[0] if not fep_contrib_nonzero.empty else 0

        # TODO: Update the database query to include GOT payment information
        got_payment = 0  # Always 0 until database query is updated
        ror_payment = float(employee_data[0].get('WPL_Wartosc', 0) or 0)  # Use the first value

        # Calculate ZUS totals
        total_emp_zus = pension_contrib_emp + disability_contrib_emp + sickness_contrib

        # Calculate subtotal for E, R, W components (used in the Razem row)
        firm_zus_subtotal = pension_contrib_firm + disability_contrib_firm + accident_contrib_firm

        # Group and sum WPE_Wartosc values by TWP_Skrot
        grouped_items = {}
        total_opodat = 0
        total_nieopodat = 0

        for row in employee_data:
            wpe_wartosc = row.get('WPE_Wartosc', 0)
            twp_skrot = row.get('TWP_Skrot', '')

            # Skip empty or zero values
            if pd.isna(wpe_wartosc) or wpe_wartosc == 0 or not twp_skrot:
                continue

            # Convert to float to ensure proper comparison
            wpe_wartosc = float(wpe_wartosc)

            # Initialize entry if not exists
            if twp_skrot not in grouped_items:
                grouped_items[twp_skrot] = {'OPODAT': 0, 'NIEOPODAT': 0}

            # Check TWP_RodzajFIS to determine if OPODAT or NIEOPODAT
            # TWP_RodzajFIS=1 indicates NIEOPODAT, any other value indicates OPODAT
            twp_rodzaj_fis = row.get('TWP_RodzajFIS', 0)

            if twp_rodzaj_fis == 1:
                # If TWP_RodzajFIS=1, add to NIEOPODAT
                grouped_items[twp_skrot]['NIEOPODAT'] += wpe_wartosc
                # Keep track of total for summary
                total_nieopodat += wpe_wartosc
            else:
                # Otherwise, add to OPODAT
                grouped_items[twp_skrot]['OPODAT'] += wpe_wartosc
                # Keep track of total for summary
                total_opodat += wpe_wartosc

        # Convert grouped dictionary to list for display
        brutto_items = []
        for twp_skrot, values in grouped_items.items():
            brutto_items.append({
                'TWP_Skrot': twp_skrot,
                'OPODAT': values['OPODAT'],
                'NIEOPODAT': values['NIEOPODAT']
            })

        # Sort items by value (high to low)
        brutto_items.sort(key=lambda x: abs(x['OPODAT']) + abs(x['NIEOPODAT']), reverse=True)

        # total_nieopodat is already calculated during the loop

        # Add a total item at the end
        if brutto_items:
            # Add a separator line before RAZEM
            brutto_items.append({
                'TWP_Skrot': '────────────────────────────────────────',
                'OPODAT': '──────────',
                'NIEOPODAT': '──────────'
            })
            # Add the RAZEM row
            brutto_items.append({
                'TWP_Skrot': 'RAZEM',  # Use full word instead of abbreviation
                'OPODAT': total_opodat,
                'NIEOPODAT': total_nieopodat
            })

        # Create data structure with employee info and WPE_Wartosc values side by side
        data = []

        # Employee data rows
        first_row = [
            str(idx + 1),
            last_name,
            "",
            "",
            "",
            f"E: {format_currency(pension_contrib_emp)}",
            f"E: {format_currency(pension_contrib_firm)}",
            f"Zal: {format_currency(tax_advance)}",
            f"KWOTA: {format_currency(got_payment + ror_payment)}"
        ]
        data.append(first_row)

        second_row = [
            "",
            akronim,
            "",
            "",
            "",
            f"R: {format_currency(disability_contrib_emp)}",
            f"R: {format_currency(disability_contrib_firm)}",
            f"Zdr: {format_currency(health_contrib)}",
            ""
        ]
        data.append(second_row)

        third_row = [
            "",
            first_name,
            "",
            "",
            "",
            f"C: {format_currency(sickness_contrib)}",
            f"W: {format_currency(accident_contrib_firm)}",
            f"K: {format_currency(tax_costs)}",
            ""
        ]
        data.append(third_row)

        # Add FP, FGŚP, FEP rows before Razem
        fourth_row = [
            "",
            "",
            "",
            "",
            "",
            "",
            f"FP: {format_currency(fp_contrib)}",
            f"Ulga: {format_currency(tax_relief)}",
            ""
        ]
        data.append(fourth_row)

        fifth_row = [
            "",
            "",
            "",
            "",
            "",
            "",
            f"FGŚP: {format_currency(fgsp_contrib)}",
            "",
            ""
        ]
        data.append(fifth_row)

        sixth_row = [
            "",
            "",
            "",
            "",
            "",
            "",
            f"FEP: {format_currency(fep_contrib)}",
            "",
            ""
        ]
        data.append(sixth_row)

        # Calculate total including all components
        total_firm_zus = firm_zus_subtotal + fp_contrib + fgsp_contrib + fep_contrib

        # Add Razem row after FP, FGŚP, FEP
        razem_row = [
            "",
            "",
            "",
            "",
            "",
            f"Razem: {format_currency(total_emp_zus)}",
            f"Razem: {format_currency(total_firm_zus)}",
            "",
            ""
        ]
        data.append(razem_row)

        # Add a horizontal line after Razem (before PPK) in UBEZPIECZ and PLATNIK columns
        separator_row = [
            "",
            "",
            "",
            "",
            "",
            "────────────",
            "────────────",
            "",
            ""
        ]
        data.append(separator_row)

        # Add WPE_Wartosc items
        for i, item in enumerate(brutto_items):
            if i < len(data):
                data[i][2] = wrap_text(item['TWP_Skrot'], 40)
                data[i][3] = format_currency(item['OPODAT'])
                data[i][4] = format_currency(item['NIEOPODAT'])
            else:
                data.append([
                    "",
                    "",
                    wrap_text(item['TWP_Skrot'],40),
                    format_currency(item['OPODAT']),
                    format_currency(item['NIEOPODAT']),
                    "",
                    "",
                    "",
                    ""
                ])

        # Add additional rows for ZUS totals
        data.append([
            "",
            f"PLN/mies.: {format_currency(gross_salary)}",
            "",
            "",
            "",
            "",
            "",
            "",
            ""
        ])

        # Calculate PPK values for this employee - take first non-zero value to avoid double counting
        ppk_p_ubezpiecz_values = emp_df['WPE_SklPPKPrac1'].fillna(0).astype(float)
        ppk_p_ubezpiecz_nonzero = ppk_p_ubezpiecz_values[ppk_p_ubezpiecz_values != 0]
        ppk_p_ubezpiecz = ppk_p_ubezpiecz_nonzero.iloc[0] if not ppk_p_ubezpiecz_nonzero.empty else 0

        ppk_p_platnik_values = emp_df['WPE_SklPPKFirma1'].fillna(0).astype(float)
        ppk_p_platnik_nonzero = ppk_p_platnik_values[ppk_p_platnik_values != 0]
        ppk_p_platnik = ppk_p_platnik_nonzero.iloc[0] if not ppk_p_platnik_nonzero.empty else 0

        ppk_d_ubezpiecz_values = emp_df['WPE_SklPPKPrac2'].fillna(0).astype(float)
        ppk_d_ubezpiecz_nonzero = ppk_d_ubezpiecz_values[ppk_d_ubezpiecz_values != 0]
        ppk_d_ubezpiecz = ppk_d_ubezpiecz_nonzero.iloc[0] if not ppk_d_ubezpiecz_nonzero.empty else 0

        ppk_d_platnik_values = emp_df['WPE_SklPPKFirma2'].fillna(0).astype(float)
        ppk_d_platnik_nonzero = ppk_d_platnik_values[ppk_d_platnik_values != 0]
        ppk_d_platnik = ppk_d_platnik_nonzero.iloc[0] if not ppk_d_platnik_nonzero.empty else 0

        # Add PPK rows with real values - Both PPK P and PPK D in both UBEZPIECZ and PLATNIK columns
        data.append([
            "",
            "",
            "",
            "",
            "",
            f"PPK P: {format_currency(ppk_p_ubezpiecz)}",
            f"PPK P: {format_currency(ppk_p_platnik)}",
            "",
            ""
        ])

        # Add second PPK row for PPK D values
        data.append([
            "",
            "",
            "",
            "",
            "",
            f"PPK D: {format_currency(ppk_d_ubezpiecz)}",
            f"PPK D: {format_currency(ppk_d_platnik)}",
            "",
            ""
        ])

        # Add PPK sum rows with real values
        data.append([
            "",
            "",
            "",
            "",
            "",
            f"Razem: {format_currency(ppk_p_ubezpiecz + ppk_d_ubezpiecz)}",
            f"Razem: {format_currency(ppk_p_platnik + ppk_d_platnik)}",
            "",
            ""
        ])

        # Column widths in characters
        widths = [4, 22, 42, 12, 12, 16, 16, 16, 18]

        # Column alignments
        align = ['center', 'left', 'left', 'right', 'right', 'left', 'left', 'left', 'right']

        # Create a smaller monospace style with left indent to move tables to the left
        # Match the formatting of the summary table with reduced font size and line spacing
        smaller_monospace_style = ParagraphStyle(
            'SmallerMonospaceStyle',
            parent=styles['monospace'],
            fontSize=5.5,  # Reduced font size to match summary table
            alignment=TA_LEFT,
            leftIndent=-2,  # Move table to the left
            spaceAfter=0,
            leading=6  # Reduced line spacing to match summary table
        )

        # For the first employee, include headers; for others, create a continuous table
        if idx == 0:
            # First employee gets full table with headers
            employee_table = create_ascii_table(headers, data, widths, align, subheaders)
        else:
            # Subsequent employees get table without headers (continuous table)
            employee_table = create_ascii_table([], data, widths, align, [])

        # Add the employee table to the content
        content.append(Preformatted(employee_table, smaller_monospace_style))

        # Add a thin separator line between employees to differentiate them
        # but still maintain a seamless connection with solid vertical bars
        if idx < len(employee_list) - 1:  # Don't add after the last employee
            separator_line = "├"
            for width in widths:
                separator_line += "─" * (width) + "┼"
            separator_line = separator_line[:-1] + "┤"
            content.append(Preformatted(separator_line, smaller_monospace_style))

    # Add a bottom border to the last employee table with solid vertical bars
    bottom_border = "└"
    for width in widths:
        bottom_border += "─" * (width) + "┴"
    bottom_border = bottom_border[:-1] + "┘"
    content.append(Preformatted(bottom_border, smaller_monospace_style))

    # Add a smaller spacer before the summary table
    content.append(Spacer(1, 2*mm))

    # Create summary table
    summary_table = create_summary_table(summary_data)

    # Add a title for the summary section with reduced spacing
    title_style = ParagraphStyle(
        'SummaryTitleStyle',
        parent=styles['title'],
        fontSize=8,
        alignment=TA_CENTER,
        spaceAfter=1*mm
    )

    content.append(Preformatted("PODSUMOWANIE LISTY PŁAC", title_style))
    content.append(Spacer(1, 1*mm))

    # Add the summary table with even smaller font and moved to the left
    smaller_monospace_style = ParagraphStyle(
        'SmallerMonospaceStyle',
        parent=styles['monospace'],
        fontSize=5.5,  # Reduced font size
        alignment=TA_LEFT,
        leftIndent=-2,  # Move table to the left
        spaceAfter=0,
        leading=6  # Reduced line spacing
    )

    # Add the summary table to the content
    content.append(Preformatted(summary_table, smaller_monospace_style))

    # Build the PDF
    try:
        doc.build(content, onFirstPage=first_page, onLaterPages=later_pages)

        if return_data:
            # Get the PDF data from the buffer
            pdf_data = buffer.getvalue()
            buffer.close()
            print(f"Payroll PDF data generated ({len(pdf_data)} bytes)")
            return pdf_data
        else:
            print(f"Payroll PDF generated: {output_file}")
            return output_file
    except Exception as e:
        print(f"Error during PDF document build: {e}")
        return None

def process_all_databases_and_return_data():
    """
    Process data from all databases using ConnectDB.get_all_data() and return PDF data for each
    unique combination of database name and list number.

    Returns:
        list: A list of dictionaries containing the database name, list number (LPL_NumerPelny),
              PDF data, and additional metadata for each unique combination
    """
    print("Starting payroll PDF data generation for all databases...")

    pdf_data_list = []

    try:
        # Get data from all databases using ConnectDB.get_all_data()
        df_all = get_all_data()

        if df_all.empty:
            print("No data returned from get_all_data().")
            return pdf_data_list

        print(f"Retrieved data from all databases: {len(df_all)} total rows.")

        # Group data by database and list number
        grouped_data = df_all.groupby(['Baza', 'LPL_NumerPelny'])

        # Get list of unique combinations
        combinations = list(grouped_data.groups.keys())

        print(f"Found {len(combinations)} unique database/list number combinations.")

        # Process each combination separately
        for i, (baza, list_number) in enumerate(combinations):
            print(f"Processing combination {i + 1}/{len(combinations)}: {baza} - {list_number}")

            # Get the dataframe for this combination
            df_combo = grouped_data.get_group((baza, list_number))

            # Extract additional fields
            nazwa = df_combo['Nazwa'].iloc[0] if 'Nazwa' in df_combo.columns else None
            email_oka = df_combo['emailOka'].iloc[0] if 'emailOka' in df_combo.columns else None
            email_k = df_combo['emailK'].iloc[0] if 'emailK' in df_combo.columns else None
            nip = df_combo['Nip'].iloc[0] if 'Nip' in df_combo.columns else None

            try:
                print(f"Generating payroll PDF data for {baza} - {list_number}")
                pdf_data = generate_payroll_pdf(df_combo, list_number=list_number, return_data=True)
                if pdf_data:
                    print(f"PDF data for {baza} - {list_number} generated successfully ({len(pdf_data)} bytes)")
                    pdf_data_list.append({
                        "baza": baza,
                        "list_number": list_number,
                        "pdf_data": pdf_data,
                        "nazwa": nazwa,
                        "emailOka": email_oka,
                        "emailK": email_k,
                        "nip": nip
                    })
                else:
                    print(f"Failed to generate PDF data for {baza} - {list_number}.")
            except Exception as e:
                print(f"Error generating PDF data for {baza} - {list_number}: {e}")
                import traceback
                print(f"Detailed error: {traceback.format_exc()}")

        return pdf_data_list

    except Exception as e:
        print(f"Error processing databases: {e}")
        import traceback
        print(f"Detailed error: {traceback.format_exc()}")
        return pdf_data_list



# Escape file names to remove illegal characters
def escape_filename(filename):
    """
    Escape a filename to remove illegal characters.

    Args:
        filename (str): The filename to escape

    Returns:
        str: The escaped filename
    """
    # Replace illegal characters with underscores
    illegal_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
    for char in illegal_chars:
        filename = filename.replace(char, '_')
    return filename


