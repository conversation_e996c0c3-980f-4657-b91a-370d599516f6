from sql_to_pdf import process_all_databases_and_return_data
from pypdf import Pdf<PERSON><PERSON><PERSON>, PdfWriter
from io import BytesIO
import re
import os

def sanitize_filename(filename: str) -> str:
    """
    Sanitizes filename by removing or replacing invalid characters for Windows.

    Args:
        filename (str): Original filename

    Returns:
        str: Sanitized filename safe for Windows file system
    """
    # Replace invalid characters with underscores
    invalid_chars = r'<>:"/\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')

    # Remove any trailing dots or spaces
    filename = filename.rstrip('. ')

    return filename


def download_pdf(pdf_data: bytes, output_path: str):
    """
    Zapisuje plik PDF na dysku.

    Args:
        pdf_data (bytes): Dane binarne pliku PDF
        output_path (str): Ścieżka do zapisu (np. 'C:/ścieżka/nazwa_pliku.pdf')
    """
    try:
        with open(output_path, 'wb') as f:
            f.write(pdf_data)
        print(f"PDF zapisany w: {output_path}")
    except Exception as e:
        print(f"Błąd zapisu PDF: {e}")


def encrypt_pdf_with_nip_password(pdf_data: bytes, nip: str, output_path: str = None) -> bytes:
    if nip is None or not str(nip).strip() or len(nip) < 4:
        return None

    cleaned_nip = re.sub(r'\D', '', str(nip))
    password = cleaned_nip[-4:]

    input_stream = BytesIO(pdf_data)
    reader = PdfReader(input_stream)
    writer = PdfWriter()

    for page in reader.pages:
        writer.add_page(page)

    writer.encrypt(user_password=password, owner_password=None)

    output_stream = BytesIO()
    writer.write(output_stream)
    encrypted_pdf_data = output_stream.getvalue()

    if output_path:
        # Upewnij się, że folder istnieje
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'wb') as f:
            f.write(encrypted_pdf_data)
        print(f"Zaszyfrowany PDF zapisany do pliku: {output_path}")

    print("Dokument PDF zaszyfrowany poprawnie")
    return encrypted_pdf_data


def main():
    """
    Main function to process all databases and send emails with PDFs grouped by database.
    """
    pdf_data_list = process_all_databases_and_return_data()

    if not pdf_data_list:
        print("Brak danych do przetworzenia.")
        return

    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)

    for idx, entry in enumerate(pdf_data_list, start=1):
        baza = entry['baza']
        list_number = entry['list_number']
        pdf_data = entry['pdf_data']
        nazwa = entry['nazwa']
        nip = entry['nip']


        print(f"Zaczynam szyfrowanie pdf dla bazy: {baza} i nipu {nip}")

        output_filename = f"umowa_{nazwa}_{list_number}.pdf"
        output_filename = sanitize_filename(output_filename)
        output_path = os.path.join(output_folder, output_filename)

        encrypt_pdf_with_nip_password(pdf_data=pdf_data, nip=nip, output_path=output_path)
main()