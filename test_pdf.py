from sql_to_pdf import process_all_databases_and_return_data, generate_payroll_pdf
from ConnectDB import get_all_data
from pypdf import PdfReader, PdfWriter
from io import BytesIO
import re
import os
import pandas as pd

def sanitize_filename(filename: str) -> str:
    """
    Sanitizes filename by removing or replacing invalid characters for Windows.

    Args:
        filename (str): Original filename

    Returns:
        str: Sanitized filename safe for Windows file system
    """
    # Replace invalid characters with underscores
    invalid_chars = r'<>:"/\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')

    # Remove any trailing dots or spaces
    filename = filename.rstrip('. ')

    return filename


def download_pdf(pdf_data: bytes, output_path: str):
    """
    Zapisuje plik PDF na dysku.

    Args:
        pdf_data (bytes): Dane binarne pliku PDF
        output_path (str): Ścieżka do zapisu (np. 'C:/ścieżka/nazwa_pliku.pdf')
    """
    try:
        with open(output_path, 'wb') as f:
            f.write(pdf_data)
        print(f"PDF zapisany w: {output_path}")
    except Exception as e:
        print(f"Błąd zapisu PDF: {e}")


def encrypt_pdf_with_nip_password(pdf_data: bytes, nip: str, output_path: str = None) -> bytes:
    if nip is None or not str(nip).strip() or len(nip) < 4:
        return None

    cleaned_nip = re.sub(r'\D', '', str(nip))
    password = cleaned_nip[-4:]

    input_stream = BytesIO(pdf_data)
    reader = PdfReader(input_stream)
    writer = PdfWriter()

    for page in reader.pages:
        writer.add_page(page)

    writer.encrypt(user_password=password, owner_password=None)

    output_stream = BytesIO()
    writer.write(output_stream)
    encrypted_pdf_data = output_stream.getvalue()

    if output_path:
        # Upewnij się, że folder istnieje
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'wb') as f:
            f.write(encrypted_pdf_data)
        print(f"Zaszyfrowany PDF zapisany do pliku: {output_path}")

    print("Dokument PDF zaszyfrowany poprawnie")
    return encrypted_pdf_data


def test_specific_database_and_invoice(database_name: str, invoice_pattern: str):
    """
    Test function to process a specific database and invoice number pattern.

    Args:
        database_name (str): Name of the database to process (e.g., "CDN_Nesc_sp__z_o_o_")
        invoice_pattern (str): Invoice number pattern to filter (e.g., "E/2025/05/*")
    """
    print(f"Testing with database: {database_name}")
    print(f"Invoice pattern: {invoice_pattern}")

    # Get all data from databases
    df_all = get_all_data()

    if df_all.empty:
        print("No data returned from get_all_data().")
        return

    print(f"Total rows retrieved: {len(df_all)}")

    # Filter by specific database
    df_filtered = df_all[df_all['Baza'] == database_name]

    if df_filtered.empty:
        print(f"No data found for database: {database_name}")
        print("Available databases:")
        print(df_all['Baza'].unique())
        return

    print(f"Rows for database {database_name}: {len(df_filtered)}")

    # Filter by invoice pattern (convert pattern to regex)
    # E/2025/05/* becomes E/2025/05/.*
    regex_pattern = invoice_pattern.replace('*', '.*')
    df_invoice_filtered = df_filtered[df_filtered['LPL_NumerPelny'].str.match(regex_pattern, na=False)]

    if df_invoice_filtered.empty:
        print(f"No data found for invoice pattern: {invoice_pattern}")
        print("Available invoice numbers for this database:")
        print(df_filtered['LPL_NumerPelny'].unique())
        return

    print(f"Rows matching invoice pattern: {len(df_invoice_filtered)}")
    print("Matching invoice numbers:")
    print(df_invoice_filtered['LPL_NumerPelny'].unique())

    # Group by LPL_NumerPelny and process each invoice
    grouped_data = df_invoice_filtered.groupby('LPL_NumerPelny')

    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)

    for list_number, df_group in grouped_data:
        print(f"\nProcessing invoice: {list_number}")

        # Extract metadata
        nazwa = df_group['Nazwa'].iloc[0] if 'Nazwa' in df_group.columns else "Unknown"
        nip = df_group['Nip'].iloc[0] if 'Nip' in df_group.columns else "Unknown"

        try:
            # Generate PDF
            pdf_data = generate_payroll_pdf(df_group, list_number=list_number, return_data=True)

            if pdf_data:
                print(f"PDF generated successfully for {list_number} ({len(pdf_data)} bytes)")

                # Create output filename
                output_filename = f"test_{database_name}_{list_number}.pdf"
                output_filename = sanitize_filename(output_filename)
                output_path = os.path.join(output_folder, output_filename)

                # Encrypt and save PDF
                encrypt_pdf_with_nip_password(pdf_data=pdf_data, nip=nip, output_path=output_path)

            else:
                print(f"Failed to generate PDF for {list_number}")

        except Exception as e:
            print(f"Error processing {list_number}: {e}")
            import traceback
            print(f"Detailed error: {traceback.format_exc()}")


def main():
    """
    Main function to process all databases and send emails with PDFs grouped by database.
    """
    pdf_data_list = process_all_databases_and_return_data()

    if not pdf_data_list:
        print("Brak danych do przetworzenia.")
        return

    output_folder = 'output'
    os.makedirs(output_folder, exist_ok=True)

    for idx, entry in enumerate(pdf_data_list, start=1):
        baza = entry['baza']
        list_number = entry['list_number']
        pdf_data = entry['pdf_data']
        nazwa = entry['nazwa']
        nip = entry['nip']


        print(f"Zaczynam szyfrowanie pdf dla bazy: {baza} i nipu {nip}")

        output_filename = f"umowa_{nazwa}_{list_number}.pdf"
        output_filename = sanitize_filename(output_filename)
        output_path = os.path.join(output_folder, output_filename)

        encrypt_pdf_with_nip_password(pdf_data=pdf_data, nip=nip, output_path=output_path)


if __name__ == "__main__":
    # Test with specific database and invoice pattern
    test_specific_database_and_invoice("CDN_Nesc_sp__z_o_o_", "E/2025/05/*")

    # Uncomment the line below to run the original main function instead
    # main()