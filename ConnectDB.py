import os
from sqlalchemy import create_engine
from dotenv import load_dotenv
from sqlalchemy import text
import pandas as pd
import SendSMS

load_dotenv()
db_username = os.getenv("DB_USERNAME")
db_password = os.getenv("DB_PASSWORD")
db_host = os.getenv("DB_HOST")
db_port = os.getenv("DB_PORT", "1433")
db_name = os.getenv("DB_NAME")

DATABASE_URL = f"mssql+pyodbc://{db_username}:{db_password}@{db_host}:{db_port}/{db_name}?driver=ODBC+Driver+17+for+SQL+Server"

engine = create_engine(DATABASE_URL)

def get_doc_number(database):
    with engine.connect() as conn:
        result = conn.execute(text(f"""
            select LPL_NumerPelny
            from {database}.cdn.ListyPlac
            where
            LPL_TS_Mod >= DATEADD(HOUR, -4, GETDATE())
            AND LPL_Miesiac = MONTH(GETDATE())
            AND LPL_Rok = YEAR(GETDATE())
            and LPL_Zamknieta is not null
            AND LEFT(LPL_NumerPelny,2) <> 'W/'
        """))
        return [row[0] for row in result.fetchall()]
def get_email_data():
    with engine.connect() as conn:
        result = conn.execute(text("""
            SELECT
                b.Baz_NazwaBazy,
                ISNULL(KnO_Email, '<EMAIL>') AS emailK,
                '<EMAIL>' AS emailOka,
                CONCAT(kh.Knt_Nazwa1, kh.Knt_Nazwa2, kh.Knt_Nazwa3) AS Nazwa,
                ISNULL(kh.Knt_Nip, b.Baz_Nip) AS Nip
            FROM CDN_KNF_Konfiguracja_polska.CDN.Bazy b
            LEFT JOIN CDN_TAXCOACH.CDN.Kontrahenci kh
                ON kh.Knt_BazaBR_GUID = b.Baz_GUID
            LEFT JOIN CDN_TAXCOACH.CDN.KntOsoby os
                ON os.KnO_KntId = kh.Knt_KntId
                AND KnO_Nieaktywny = 0
                AND os.KnO_Tytul = 'Powiadomienia'
            LEFT JOIN CDN_KNF_Konfiguracja_polska.CDN.Operatorzy op
                ON kh.Knt_OpiekunKsiegID = op.Ope_OpeID
            INNER JOIN sys.databases db
                ON db.name = b.Baz_NazwaBazy
            WHERE db.state_desc = 'ONLINE'
              AND Baz_Nieaktywna = 0
              AND LEFT(Baz_Nazwa, 2) <> 'N_'       
        """))
        rows = result.fetchall()
        columns = result.keys()
        df_query = pd.DataFrame(rows, columns=columns)


        return df_query




def get_all_data():
    """
    Get data from all databases.

    Returns:
        DataFrame: DataFrame containing the data from all databases
    """
    # Pobierz dane kontekstowe
    email_df = get_email_data()
    all_dataframes = []

    with engine.connect() as conn:
        for _, row in email_df.iterrows():
            baza = row['Baz_NazwaBazy'].replace("'", "''")  # Escape single quotes in database name too
            nazwa = row['Nazwa'].replace("'", "''")  # Escape single quotes by doubling them
            email_oka = row['emailOka'].replace("'", "''")
            email_k = row['emailK'].replace("'", "''")
            nip = row['Nip'].replace("'", "''") if row['Nip'] is not None else ''
            document_list = get_doc_number(baza)
            if document_list:
                for elem in document_list:
                    sql = f"""
                        SELECT DISTINCT
                            '{baza}' AS Baza,
                            '{nazwa}' AS Nazwa,
                            '{email_oka}' AS emailOka,
                            '{email_k}' AS emailK,
                            '{nip}' AS Nip,
                            LPL_NumerPelny,
                            LPL_Nazwa,
                            LPL_LplId,
                            CAST(LPL_DataOd AS DATE) AS LPL_DataOd,
                            CAST(LPL_DataDo AS DATE) AS LPL_DataDo,
                            CAST(LPL_DataDok AS DATE) AS LPL_DataDok,
                            WPL_DataDok,
                            WPL_Wartosc,
                            PRE_ETAStawka,
                            PRI_Kod,
                            PRI_Nazwisko,
                            PRI_Imie1,
                            WPE_WpeId,
                            WPE_Wartosc,
                            WPE_Ulga,
                            WPE_Koszty,
                            WPE_ZalFis,
                            WPE_NalFis,
                            WPE_SklEmerPrac,
                            WPE_SklEmerFirma,
                            WPE_SklRentPrac,
                            WPE_SklRentFirma,
                            WPE_SklChorPrac,
                            WPE_SklChorFirma,
                            WPE_SklWypadPrac,
                            WPE_SklWypadFirma,
                            WPE_SklFP,
                            WPE_SklFGSP,
                            WPE_SklZdrowPrac,
                            WPE_Netto,
                            WPE_PodstEmer,
                            WPE_PodstRent,
                            WPE_PodstChor,
                            WPE_PodstWypad,
                            WPE_PodstFP,
                            WPE_PodstFGSP,
                            WPE_PodstFEP,
                            WPE_SklEmerPracZwol,
                            WPE_SklRentPracZwol,
                            WPE_SklChorPracZwol,
                            TWP_Skrot,
                            TWP_RodzajFIS,
                            TWP_SklZUS,
                            WPE_SklPPKPrac1,
                            WPE_SklPPKPrac2,
                            WPE_SklPPKFirma1,
                            WPE_SklPPKFirma2
                        FROM [{row['Baz_NazwaBazy']}].cdn.ListyPlac AS lp
                        LEFT JOIN [{row['Baz_NazwaBazy']}].cdn.Wyplaty AS wyp ON wyp.WPL_LplId = lp.LPL_LplId
                        LEFT JOIN [{row['Baz_NazwaBazy']}].cdn.PracEtaty AS pr ON pr.PRE_PraId = wyp.WPL_PraId
                        LEFT JOIN [{row['Baz_NazwaBazy']}].cdn.Pracidx AS praid ON praid.PRI_PraId = pr.PRE_PraId
                        LEFT JOIN [{row['Baz_NazwaBazy']}].cdn.WypElementy AS elw ON elw.WPE_WplId = wyp.WPL_WplId
                        LEFT JOIN [{row['Baz_NazwaBazy']}].cdn.TypWyplata AS typw ON typw.TWP_TwpId = elw.WPE_TwpId
                        WHERE lp.LPL_NumerPelny = '{elem}'
                    """
                    # print(sql)

                    try:
                        result = conn.execute(text(sql))
                        rows = result.fetchall()
                        if rows:
                            columns = result.keys()
                            df_single = pd.DataFrame(rows, columns=columns)
                            all_dataframes.append(df_single)
                            print(f"Pomyślnie pobrano dane z bazy: {baza}")
                        else:
                            print(f"Brak danych dla bazy: {baza}")
                    except Exception as e:
                        print(f"Błąd przy bazie {baza}: {e}")
                        # Print more detailed error information for debugging
                        import traceback
                        print(f"Szczegóły błędu: {traceback.format_exc()}")

    # Łączenie wyników
    if all_dataframes:
        final_df = pd.concat(all_dataframes, ignore_index=True)
    else:
        final_df = pd.DataFrame()

    return final_df



def query_execute():
    with engine.connect() as conn:
        query_result = conn.execute(text("SELECT * FROM dbo.WysylkaDeklaracjiSMS;"))
        df_query = pd.DataFrame(query_result)
        df_current = df_query[df_query['smsWyslany'] == '0']
        df_current['numerTel'] = (
            df_current['numerTel']
            .fillna('')
            .astype(str)
            .str.replace(' ', '', regex=True)
            .apply(lambda x: '48' + x if x else '')
        )
        return df_current


def insert():
    id_list = SendSMS.send_sms()
    if not id_list:
        print("Lista jest pusta.")
    else:

        for elem in id_list:
            print(elem)

        id_string = ','.join(map(str, id_list))

        with engine.connect() as conn:
            with conn.begin():
                query_result = conn.execute(text(
                    f"UPDATE dbo.WysylkaDeklaracjiSMS SET smsWyslany = '1', dataWyslaniaSMS = GETDATE() WHERE ID IN ({id_string});"))



        print(id_string)


