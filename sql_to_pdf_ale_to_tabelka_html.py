import os
import pandas as pd
import datetime
import locale
from jinja2 import Environment, FileSystemLoader, select_autoescape, Template # Added Template for inline
from weasyprint import HTML, CSS
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# --- Database Connection and Data Fetching (from original ConnectDB.py block) ---
load_dotenv()
db_username = os.getenv("DB_USERNAME")
db_password = os.getenv("DB_PASSWORD")
db_host = os.getenv("DB_HOST")
db_port = os.getenv("DB_PORT", "1433")
db_name = os.getenv("DB_NAME") # This seems to be a default DB, not used for specific company DBs

# Check if essential DB connection variables are loaded
if not all([db_username, db_password, db_host, db_port]):
    print("Error: Database connection details (DB_USERNAME, DB_PASSWORD, DB_HOST, DB_PORT) not found in .env file.")
    print("Please ensure a .env file is present and correctly configured.")
    # exit(1) # Or handle this more gracefully depending on expected environment

# Construct DATABASE_URL carefully, handle cases where db_name might not be needed for server-level access
# For listing databases or connecting to CDN_KNF_Konfiguracja_polska, a specific db_name in URL might be an issue
# Let's assume master or a default db is fine for initial connection, then queries specify DB.
# If get_email_data needs a specific DB context in the URL, this might need adjustment.
# Typically, for SQL Server, you can connect to master and then use `USE [DatabaseName]` or fully qualified names.
# The provided queries use fully qualified names like `CDN_KNF_Konfiguracja_polska.CDN.Bazy`.

try:
    # Connect to a default database, often 'master' if db_name is not for this specific purpose
    # Or, if db_name IS a specific database required for these meta-queries, ensure it's set.
    # For now, using 'master' as a placeholder if db_name from .env is not 'CDN_KNF_Konfiguracja_polska'
    # The queries themselves qualify the database names, so the initial DB in connection string is less critical.
    connection_db = db_name if db_name else "master"
    DATABASE_URL = f"mssql+pyodbc://{db_username}:{db_password}@{db_host}:{db_port}/{connection_db}?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes"
    engine = create_engine(DATABASE_URL)
except Exception as e:
    print(f"Error creating database engine: {e}")
    # exit(1)


def get_doc_number(database_name_param, conn_param): # Renamed to avoid conflict, pass connection
    # Correctly quote database name if it contains special characters or is a variable
    # However, f-string formatting for database names in SQL is risky.
    # It's better if such parts are parameters or carefully sanitized.
    # For now, assuming database_name_param is safe.
    # Ensure the database name is properly escaped if it can contain spaces or special chars,
    # though typically they don't for SQL Server database names used in `FROM [dbname].schema.table`.
    # The original query used f-string directly: f"select ... from {database}.cdn.ListyPlac"
    # This is generally okay if `database` is a controlled variable.
    query = text(f"""
        SELECT LPL_NumerPelny
        FROM [{database_name_param}].cdn.ListyPlac
        WHERE
        LPL_Miesiac = MONTH(GETDATE())
        AND LPL_Rok = YEAR(GETDATE())
        AND LPL_Zamknieta IS NOT NULL
    """)
    result = conn_param.execute(query)
    return [row[0] for row in result.fetchall()]

def get_email_data(conn_param): # Renamed, pass connection
    # This query is specific to 'CDN_Nesc_sp__z_o_o_' as per original
    # Let's keep it that way for this targeted refactoring.
    query = text("""
        SELECT
            b.Baz_NazwaBazy,
            ISNULL(KnO_Email, '<EMAIL>') AS emailK,
            '<EMAIL>' AS emailOka,
            CONCAT(kh.Knt_Nazwa1, kh.Knt_Nazwa2, kh.Knt_Nazwa3) AS Nazwa,
            ISNULL(kh.Knt_Nip, '') AS Nip
        FROM CDN_KNF_Konfiguracja_polska.CDN.Bazy b
        LEFT JOIN CDN_TAXCOACH.CDN.Kontrahenci kh
            ON kh.Knt_BazaBR_GUID = b.Baz_GUID
        LEFT JOIN CDN_TAXCOACH.CDN.KntOsoby os
            ON os.KnO_KntId = kh.Knt_KntId
            AND KnO_Nieaktywny = 0
            AND os.KnO_Tytul = 'Powiadomienia'
        LEFT JOIN CDN_KNF_Konfiguracja_polska.CDN.Operatorzy op
            ON kh.Knt_OpiekunKsiegID = op.Ope_OpeID
        INNER JOIN sys.databases db
            ON db.name = b.Baz_NazwaBazy
        WHERE db.state_desc = 'ONLINE'
          AND Baz_Nieaktywna = 0
          AND LEFT(Baz_Nazwa, 2) <> 'N_'
          AND Baz_NazwaBazy = 'CDN_Nesc_sp__z_o_o_' -- Specific filter
    """)
    result = conn_param.execute(query)
    rows = result.fetchall()
    columns = result.keys()
    df_query = pd.DataFrame(rows, columns=columns)
    return df_query

def get_payroll_data_for_db(target_db_name):
    all_dataframes = []
    try:
        with engine.connect() as conn:
            email_df = get_email_data(conn) # This will fetch for 'CDN_Nesc_sp__z_o_o_'

            # Filter email_df for the target_db_name, though get_email_data is already specific
            db_info_row = email_df[email_df['Baz_NazwaBazy'] == target_db_name]

            if db_info_row.empty:
                print(f"No configuration information found for database: {target_db_name}")
                return pd.DataFrame()

            # Take the first row if multiple, though Baz_NazwaBazy should be unique
            row = db_info_row.iloc[0]

            baza = row['Baz_NazwaBazy'] # Already target_db_name
            nazwa = str(row['Nazwa']).replace("'", "''")
            email_oka = str(row['emailOka']).replace("'", "''")
            email_k = str(row['emailK']).replace("'", "''")
            nip = str(row['Nip']).replace("'", "''")

            document_list = get_doc_number(baza, conn)
            if not document_list:
                print(f"No documents found for {baza} for the current month/year.")
                return pd.DataFrame()

            for elem_doc_num in document_list:
                elem_doc_num_escaped = str(elem_doc_num).replace("'", "''")
                sql = text(f"""
                    SELECT DISTINCT
                        '{baza}' AS Baza,
                        '{nazwa}' AS Nazwa,
                        '{email_oka}' AS emailOka,
                        '{email_k}' AS emailK,
                        '{nip}' AS Nip,
                        LPL_NumerPelny, LPL_Nazwa, LPL_LplId,
                        CAST(LPL_DataOd AS DATE) AS LPL_DataOd,
                        CAST(LPL_DataDo AS DATE) AS LPL_DataDo,
                        CAST(LPL_DataDok AS DATE) AS LPL_DataDok,
                        WPL_DataDok, WPL_Wartosc, WPL_Brutto,
                        PRI_Kod, PRI_Nazwisko, PRI_Imie1,
                        WPE_WpeId, WPE_Wartosc, WPE_Ulga, WPE_Koszty, WPE_ZalFis, WPE_NalFis,
                        WPE_SklEmerPrac, WPE_SklEmerFirma, WPE_SklRentPrac, WPE_SklRentFirma,
                        WPE_SklChorPrac, WPE_SklChorFirma, WPE_SklWypadPrac, WPE_SklWypadFirma,
                        WPE_SklFP, WPE_SklFGSP, WPE_SklZdrowPrac, WPE_Netto,
                        WPE_PodstEmer, WPE_PodstRent, WPE_PodstChor, WPE_PodstWypad,
                        WPE_PodstFP, WPE_PodstFGSP, WPE_PodstFEP,
                        WPE_SklEmerPracZwol, WPE_SklRentPracZwol, WPE_SklChorPracZwol,
                        TWP_Skrot, TWP_RodzajFIS, TWP_SklZUS,
                        WPE_SklPPKPrac1, WPE_SklPPKPrac2, WPE_SklPPKFirma1, WPE_SklPPKFirma2
                    FROM [{baza}].cdn.ListyPlac AS lp
                    LEFT JOIN [{baza}].cdn.Wyplaty AS wyp ON wyp.WPL_LplId = lp.LPL_LplId
                    LEFT JOIN [{baza}].cdn.PracEtaty AS pr ON pr.PRE_PraId = wyp.WPL_PraId
                    LEFT JOIN [{baza}].cdn.Pracidx AS praid ON praid.PRI_PraId = pr.PRE_PraId
                    LEFT JOIN [{baza}].cdn.WypElementy AS elw ON elw.WPE_WplId = wyp.WPL_WplId
                    LEFT JOIN [{baza}].cdn.TypWyplata AS typw ON typw.TWP_TwpId = elw.WPE_TwpId
                    WHERE lp.LPL_NumerPelny = '{elem_doc_num_escaped}'
                """)
                try:
                    result = conn.execute(sql)
                    rows = result.fetchall()
                    if rows:
                        columns = result.keys()
                        df_single = pd.DataFrame(rows, columns=columns)
                        all_dataframes.append(df_single)
                        print(f"Successfully fetched data for document {elem_doc_num} from database: {baza}")
                    else:
                        print(f"No data for document {elem_doc_num} in database: {baza}")
                except Exception as e_sql:
                    print(f"Error executing SQL for document {elem_doc_num} in {baza}: {e_sql}")
                    import traceback
                    print(f"Detailed error: {traceback.format_exc()}")
    except Exception as e_conn:
        print(f"Database connection or main data fetching error: {e_conn}")
        import traceback
        print(f"Detailed error: {traceback.format_exc()}")


    if all_dataframes:
        final_df = pd.concat(all_dataframes, ignore_index=True)
        return final_df
    else:
        return pd.DataFrame()

# --- End Database Connection ---


# Set locale to Polish for proper sorting of Polish characters
try:
    locale.setlocale(locale.LC_COLLATE, 'pl_PL.UTF-8')
except locale.Error:
    try:
        locale.setlocale(locale.LC_COLLATE, 'Polish_Poland.1250')
    except locale.Error:
        print("Warning: Polish locale not available. Using default locale for sorting.")
        pass

def format_currency(value, include_br=False):
    """Format a value as currency with 2 decimal places. Optionally replace \n with <br>."""
    if pd.isna(value):
        text_value = "0.00"
    else:
        try:
            text_value = f"{float(value):.2f}"
        except (ValueError, TypeError):
            text_value = str(value)

    if include_br and isinstance(text_value, str):
        return text_value.replace("\n", "<br>")
    return text_value

def calculate_summary_data(df):
    """
    Calculate summary data from all employees in the DataFrame. (Largely similar to original)
    """
    summary = {
        'wartosc_opodat': 0, 'wartosc_nieopodat': 0,
        'ubezpiecz_e': 0, 'ubezpiecz_r': 0, 'ubezpiecz_c': 0,
        'ppk_p': 0, 'ppk_d': 0, 'ppk_p_platnik': 0, 'ppk_d_platnik': 0,
        'platnik_e': 0, 'platnik_r': 0, 'platnik_w': 0, 'platnik_fp': 0,
        'platnik_fgsp': 0, 'platnik_fep': 0,
        'podatek_zal': 0, 'podatek_zdr': 0, 'podatek_k': 0, 'podatek_ulga': 0,
        'do_wyplaty_got': 0, 'do_wyplaty_ror': 0,
    }

    # Group by employee to sum unique values if necessary, or sum across all WPE records
    # The original logic seemed to sum WPE values. If an employee has multiple WPE records for a payment type,
    # they are summed. If ZUS components are per WPL (payment) and an employee has one WPL, then it's fine.
    # If ZUS is listed multiple times per employee but should only be counted once, then grouping by employee first is needed.
    # The original code iterates `df.iterrows()` and then `emp_df.iterrows()` which might imply complex aggregation.
    # Let's stick to the structure of summing across the provided WPE rows, as the original seemed to.

    for _, row in df.iterrows():
        twp_skl_zus = row.get('TWP_SklZUS', 0)
        if twp_skl_zus == 2:
            summary['ubezpiecz_e'] += float(row.get('WPE_SklEmerPrac', 0) or 0)
            summary['ubezpiecz_r'] += float(row.get('WPE_SklRentPrac', 0) or 0)
            summary['ubezpiecz_c'] += float(row.get('WPE_SklChorPrac', 0) or 0)
            summary['platnik_e'] += float(row.get('WPE_SklEmerFirma', 0) or 0)
            summary['platnik_r'] += float(row.get('WPE_SklRentFirma', 0) or 0)
            summary['platnik_w'] += float(row.get('WPE_SklWypadFirma', 0) or 0)
            summary['platnik_fp'] += float(row.get('WPE_SklFP', 0) or 0)
            summary['platnik_fgsp'] += float(row.get('WPE_SklFGSP', 0) or 0)
        
        # Assuming PodstFEP is the value to sum for FEP, and it's not conditional on TWP_SklZUS
        # Original code uses WPE_PodstFEP, if WPE_SklFEPFirma exists, it might be more appropriate.
        # For now, sticking to original's WPE_PodstFEP as a proxy if actual FEP contribution field isn't directly used.
        # If 'WPE_SklFEPFirma' is the correct field, use it. Let's assume 'WPE_PodstFEP' was a placeholder
        # and we should sum an actual FEP contribution if available, or sum the base for it.
        # The provided query doesn't have WPE_SklFEPFirma. So WPE_PodstFEP sum is likely for 'base' not 'contribution'.
        # If there's a WPE_SklFEP field for the employer's contribution, it should be used.
        # If FEP is based on WPE_PodstFEP, a rate would be needed.
        # For now, this might be an area that needs clarification based on exact schema/intent.
        # The original sums WPE_PodstFEP. If it's a base, summing bases might not be right for 'contribution'.
        # Let's assume, for now, that WPE_PodstFEP is treated as the value to report for FEP in summary.
        summary['platnik_fep'] += float(row.get('WPE_PodstFEP', 0) or 0)


        summary['ppk_p'] += float(row.get('WPE_SklPPKPrac1', 0) or 0)
        summary['ppk_d'] += float(row.get('WPE_SklPPKPrac2', 0) or 0) # Assuming D = Prac2
        summary['ppk_p_platnik'] += float(row.get('WPE_SklPPKFirma1', 0) or 0)
        summary['ppk_d_platnik'] += float(row.get('WPE_SklPPKFirma2', 0) or 0) # Assuming D = Firma2

        summary['podatek_zal'] += float(row.get('WPE_ZalFis', 0) or 0)
        summary['podatek_zdr'] += float(row.get('WPE_SklZdrowPrac', 0) or 0)
        summary['podatek_k'] += float(row.get('WPE_Koszty', 0) or 0)
        summary['podatek_ulga'] += float(row.get('WPE_Ulga', 0) or 0)

        # WPL_Wartosc is 'Do wypłaty ROR'
        # Sum WPL_Wartosc uniquely per WPL_LplId (per payment list) if an employee has multiple entries tied to same WPL_Wartosc
        # This part is tricky. The original added WPL_Wartosc from the first row of employee_data.
        # If df contains all WPE items, WPL_Wartosc might be duplicated.
        # We should sum WPL_Wartosc based on unique WPL_LplId or WPL_WplId.
        # For simplicity, sum WPL_Wartosc from df, but only for unique WPL_WplId.
        # Let's use a set to track processed WPL_WplId for summing WPL_Wartosc
        # However, the current `df` is already exploded by WPE.
        # A better approach for 'do_wyplaty_ror': sum unique WPL_Wartosc values from Wypłaty table related to this LPL.
        # The current df has WPL_Wartosc repeated. Let's sum from unique WPL_WplId if available.
        # The query provides WPL_WplId via elw.WPE_WplId = wyp.WPL_WplId. But WPL_WplId is not in SELECT.
        # Let's assume WPL_Wartosc is on each row and should be summed if it corresponds to that WPE, or it's an overall payment.
        # The original logic for summary['do_wyplaty_ror'] was:
        # summary['do_wyplaty_ror'] += float(employee_data[0].get('WPL_Wartosc', 0) or 0)
        # This means it takes WPL_Wartosc for each employee once.
        # So, we need to group by employee first for this.

        wpe_wartosc = float(row.get('WPE_Wartosc', 0) or 0)
        twp_rodzaj_fis = row.get('TWP_RodzajFIS', 0)
        if twp_rodzaj_fis == 1:
            summary['wartosc_nieopodat'] += wpe_wartosc
        else:
            summary['wartosc_opodat'] += wpe_wartosc
            
    # Correct handling of 'do_wyplaty_ror' by summing unique WPL_Wartosc per employee
    # Create a temporary df with unique employee-WPL_Wartosc pairs
    # Assuming PRI_Kod identifies an employee uniquely for this purpose
    if 'PRI_Kod' in df.columns and 'WPL_Wartosc' in df.columns:
        unique_payments = df[['PRI_Kod', 'WPL_Wartosc']].drop_duplicates()
        summary['do_wyplaty_ror'] = unique_payments['WPL_Wartosc'].sum()
    else: # Fallback if columns are missing, though they should be there
        # This fallback sums WPL_Wartosc for every WPE row, which is likely incorrect.
        # The original script's `calculate_summary_data` had a loop `for _, employee_data in employees.items():`
        # and then `summary['do_wyplaty_ror'] += float(employee_data[0].get('WPL_Wartosc', 0) or 0)`.
        # This implies summing WPL_Wartosc once per employee.
        # The current single loop over `df` is different.
        # To replicate that, we must process employees uniquely for WPL_Wartosc.
        # A simple way: create a sub-DataFrame with unique employee identifiers and their WPL_Wartosc.
        temp_df_for_payment = df[['PRI_Nazwisko', 'PRI_Imie1', 'WPL_Wartosc']].drop_duplicates()
        summary['do_wyplaty_ror'] = temp_df_for_payment['WPL_Wartosc'].astype(float).sum()


    return summary


HTML_TEMPLATE_STR = """
<html>
<head>
    <meta charset="UTF-8">
    <style>
        {{ css_styles }}
    </style>
</head>
<body>
    <div id="page-header">
        <div class="company-info">
            <div class="company-name">{{ firma_nazwa }}</div>
            <div class="company-nip">{{ firma_nip }}</div>
        </div>
        <div class="document-title-section">
            <div class="doc-title">SZCZEGÓŁOWA LISTA PŁAC</div>
            {% if lista_plac_nazwa %}
                <div class="list-name">{{ lista_plac_nazwa }}</div>
            {% endif %}
        </div>
        <div class="meta-info">
            <table>
                <tr>
                    <td>Numer: {{ list_number }}</td>
                    <td>Za okres: {{ period_start }} - {{ period_end }}</td>
                    <td>Data wypłaty: {{ payment_date }}</td>
                </tr>
            </table>
        </div>
    </div>

    <div id="page-footer">
        {{ legend_text }}
    </div>

    <div class="content">
        <table class="payroll-table">
            <thead>
                <tr>
                    <th rowspan="2" style="width: 3%;">LP</th>
                    <th rowspan="2" style="width: 13%;">PRACOWNIK</th>
                    <th rowspan="2" style="width: 28%;">EL. WYPŁATY</th>
                    <th colspan="2" style="width: 14%;">WARTOŚĆ</th>
                    <th colspan="2" style="width: 20%;">SKŁADKI</th>
                    <th rowspan="2" style="width: 11%;">PODATEK</th>
                    <th rowspan="2" style="width: 11%;">DO WYPŁATY</th>
                </tr>
                <tr>
                    <th style="width: 7%;">OPODAT</th>
                    <th style="width: 7%;">NIEOPODAT</th>
                    <th style="width: 10%;">UBEZPIECZ</th>
                    <th style="width: 10%;">PŁATNIK</th>
                </tr>
            </thead>
            <tbody>
                {% for employee in employees_data %}
                    {% for row_data in employee.table_rows %}
                    <tr class="{% if loop.last %}employee-last-data-row{% endif %}">
                        <td style="text-align: center;">{{ row_data.lp | safe }}</td>
                        <td>{{ row_data.pracownik | safe }}</td>
                        <td>{{ row_data.el_wyplaty | safe }}</td>
                        <td class="currency">{{ row_data.opodat | safe }}</td>
                        <td class="currency">{{ row_data.nieopodat | safe }}</td>
                        <td class="pre-wrapped">{{ row_data.ubezpiecz | safe }}</td>
                        <td class="pre-wrapped">{{ row_data.platnik | safe }}</td>
                        <td class="pre-wrapped">{{ row_data.podatek | safe }}</td>
                        <td class="currency pre-wrapped">{{ row_data.do_wyplaty | safe }}</td>
                    </tr>
                    {% endfor %}
                    {% if not loop.last %}
                    <tr class="employee-separator-row"><td colspan="9"></td></tr>
                    {% endif %}
                {% endfor %}
            </tbody>
        </table>

        <h3 class="summary-title">PODSUMOWANIE LISTY PŁAC</h3>
        <table class="summary-table">
            <thead>
                 <tr>
                    <th rowspan="2" class="empty-col" style="width: 3%;"></th>
                    <th rowspan="2" class="empty-col" style="width: 13%;"></th>
                    <th rowspan="2" class="empty-col" style="width: 28%;"></th>
                    <th colspan="2" style="width: 14%;">WARTOŚĆ</th>
                    <th colspan="2" style="width: 20%;">SKŁADKI</th>
                    <th rowspan="2" style="width: 11%;">PODATEK</th>
                    <th rowspan="2" style="width: 11%;">DO WYPŁATY</th>
                </tr>
                <tr>
                    <th style="width: 7%;">OPODAT</th>
                    <th style="width: 7%;">NIEOPODAT</th>
                    <th style="width: 10%;">UBEZPIECZ</th>
                    <th style="width: 10%;">PŁATNIK</th>
                </tr>
            </thead>
            <tbody>
                {% for row_data in summary_table_rows %}
                <tr>
                    <td class="empty-col">{{ row_data.c1 | safe }}</td>
                    <td class="empty-col">{{ row_data.c2 | safe }}</td>
                    <td class="empty-col">{{ row_data.c3 | safe }}</td>
                    <td class="currency">{{ row_data.opodat | safe }}</td>
                    <td class="currency">{{ row_data.nieopodat | safe }}</td>
                    <td class="pre-wrapped">{{ row_data.ubezpiecz | safe }}</td>
                    <td class="pre-wrapped">{{ row_data.platnik | safe }}</td>
                    <td class="pre-wrapped">{{ row_data.podatek | safe }}</td>
                    <td class="currency pre-wrapped">{{ row_data.do_wyplaty | safe }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</body>
</html>
"""

CSS_STYLES_STR = """
@font-face {
    font-family: 'DejaVu Sans'; /* Standard name */
    /* WeasyPrint will try to find this font on the system. */
    /* For specific ttf: src: url('file:///path/to/DejaVuSans.ttf'); */
    /* Or place ttf in a known font directory. */
}

body {
    font-family: 'DejaVu Sans', Arial, sans-serif;
    font-size: 7pt;
    line-height: 1.2;
}

@page {
    size: A4;
    margin: 30mm 10mm 20mm 10mm; /* top right bottom left */

    @top-left {
        content: element(page-header);
        vertical-align: bottom; /* Align header content to bottom of its box */
        padding-bottom: 5mm; /* Space between header content and page content */
        width: 100%;
    }

    @bottom-left {
        content: element(page-footer);
        font-size: 5pt;
        vertical-align: top;
        padding-top: 3mm;
        width: 80%; /* Give space for page number on right */
    }
    @bottom-right {
        content: "Strona " counter(page) " z " counter(pages);
        font-size: 6pt;
        vertical-align: top;
        padding-top: 3mm;
        text-align: right;
         width: 20%;
    }
}

#page-header {
    position: running(page-header);
    font-size: 7pt;
    line-height: 1.1;
}
#page-header .company-info {
    display: block; /* Changed from float for simpler block layout */
    width: 100%;
    margin-bottom: 2mm;
}
#page-header .company-name { font-weight: bold; font-size: 9pt; }
#page-header .company-nip { font-size: 7pt; }

#page-header .document-title-section {
    text-align: left; /* Changed from center to match original's left alignment for this block */
    margin-top: 2mm;
    margin-bottom: 2mm;
}
#page-header .doc-title { font-weight: bold; font-size: 12pt; }
#page-header .list-name { font-size: 9pt; margin-top:1mm;}

#page-header .meta-info table {
    width: 100%;
    font-size: 7pt;
    border-collapse: collapse; /* Ensure table structure */
}
#page-header .meta-info td {
    text-align: right; /* Data, okres, etc. right aligned in their cells */
    padding: 0 1mm; /* Minimal padding */
    width: 33%; /* Distribute space, adjust as needed */
}
#page-header .meta-info td:first-child {
    text-align: left; /* Numer: left aligned */
}


#page-footer {
    position: running(page-footer);
    white-space: normal; /* Allow wrapping for long legend */
    text-align: left;
}

.content {
    padding-top: 5mm; /* Add some space if header is very tall, to avoid overlap with main content. Or adjust @page margin-top */
}
.payroll-table, .summary-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 5mm;
    font-size: 6pt; /* Small font for dense table */
    table-layout: fixed; /* Helps with consistent column widths */
}

.payroll-table th, .payroll-table td,
.summary-table th, .summary-table td {
    border: 1px solid #555; /* Darker gray border */
    padding: 0.5mm 1mm; /* Minimal padding */
    vertical-align: top;
    text-align: left;
    line-height: 1.0; /* Very compact */
    word-wrap: break-word; /* Allow long words to break */
}

.payroll-table th, .summary-table th {
    background-color: #e8e8e8; /* Lighter gray for header */
    font-weight: bold;
    text-align: center;
    padding: 1mm;
}

.currency {
    text-align: right;
}
.pre-wrapped {
    white-space: pre-wrap; /* Honor explicit <br> or newlines if any */
}

.summary-table .empty-col {
    border: none; /* No border for placeholder cells in summary */
}
.summary-title {
    text-align: center;
    font-size: 8pt;
    font-weight: bold;
    margin-top: 5mm;
    margin-bottom: 2mm;
}
.employee-last-data-row td {
    /* border-bottom: 1.5px solid #333; Optional: Thicker border after each employee block */
}
.employee-separator-row td {
    border: none;
    border-top: 1px solid #aaa; /* A line between employees */
    height: 0px;
    padding: 0;
    line-height:0;
}

"""

def generate_payroll_pdf_weasyprint(df, output_file_base, list_number):
    if df.empty:
        print(f"No data for {list_number}, PDF not generated.")
        return None

    # Prepare data for the template
    # General document info
    period_start = df['LPL_DataOd'].iloc[0].strftime('%Y-%m-%d') if pd.notna(df['LPL_DataOd'].iloc[0]) else "N/A"
    period_end = df['LPL_DataDo'].iloc[0].strftime('%Y-%m-%d') if pd.notna(df['LPL_DataDo'].iloc[0]) else "N/A"
    payment_date = df['LPL_DataDok'].iloc[0].strftime('%Y-%m-%d') if pd.notna(df['LPL_DataDok'].iloc[0]) else "N/A"
    
    firma_nazwa = df['Nazwa'].iloc[0] if 'Nazwa' in df.columns else "Brak nazwy"
    firma_nip = f"NIP: {df['Nip'].iloc[0]}" if 'Nip' in df.columns and pd.notna(df['Nip'].iloc[0]) else "Brak NIP"
    lista_plac_nazwa = df['LPL_Nazwa'].iloc[0] if 'LPL_Nazwa' in df.columns and pd.notna(df['LPL_Nazwa'].iloc[0]) else ""

    # Group data by employee
    employees_grouped = df.groupby(['PRI_Nazwisko', 'PRI_Imie1', 'PRI_Kod'])
    
    employees_data_for_template = []
    employee_lp = 0

    # Sort employees by surname using Polish locale
    # Convert groupby object to list of tuples (name_tuple, group_df) for sorting
    sorted_employee_groups = sorted(list(employees_grouped), key=lambda x: locale.strxfrm(x[0][0]))


    for (last_name, first_name, employee_code), emp_df in sorted_employee_groups:
        employee_lp += 1
        emp_row = emp_df.iloc[0] # First row for some general employee data

        # Calculate per-employee ZUS etc. (as in original script's employee loop)
        pension_contrib_emp = 0; disability_contrib_emp = 0; sickness_contrib = 0
        pension_contrib_firm = 0; disability_contrib_firm = 0; accident_contrib_firm = 0
        fp_contrib = 0; fgsp_contrib = 0; fep_contrib = 0 # FEP for employee section

        for _, r in emp_df.iterrows():
            if r.get('TWP_SklZUS', 0) == 2:
                pension_contrib_emp += float(r.get('WPE_SklEmerPrac', 0) or 0)
                disability_contrib_emp += float(r.get('WPE_SklRentPrac', 0) or 0)
                sickness_contrib += float(r.get('WPE_SklChorPrac', 0) or 0)
                pension_contrib_firm += float(r.get('WPE_SklEmerFirma', 0) or 0)
                disability_contrib_firm += float(r.get('WPE_SklRentFirma', 0) or 0)
                accident_contrib_firm += float(r.get('WPE_SklWypadFirma', 0) or 0)
                fp_contrib += float(r.get('WPE_SklFP', 0) or 0)
                fgsp_contrib += float(r.get('WPE_SklFGSP', 0) or 0)
            # FEP logic for employee display - if different from summary
            # Assuming same WPE_PodstFEP source for display if no direct FEP contribution fields
            fep_contrib += float(r.get('WPE_PodstFEP', 0) or 0)


        health_contrib = emp_df['WPE_SklZdrowPrac'].fillna(0).astype(float).sum()
        tax_advance = emp_df['WPE_ZalFis'].fillna(0).astype(float).sum()
        tax_relief = emp_df['WPE_Ulga'].fillna(0).astype(float).sum()
        tax_costs = emp_df['WPE_Koszty'].fillna(0).astype(float).sum()
        gross_salary = emp_row.get('WPL_Brutto', 0)
        
        # WPL_Wartosc is the net payment for this employee for this LPL_LplId
        # It should be unique per employee on a given LPL.
        # If emp_df contains multiple rows for one employee from one LPL, WPL_Wartosc will be repeated.
        # Take it from emp_row (first row of emp_df) is correct.
        net_payment_ror = float(emp_row.get('WPL_Wartosc', 0) or 0)
        # got_payment = 0 # TODO: if GOT payment info becomes available

        total_emp_zus = pension_contrib_emp + disability_contrib_emp + sickness_contrib
        total_firm_zus_main = pension_contrib_firm + disability_contrib_firm + accident_contrib_firm + fp_contrib + fgsp_contrib + fep_contrib

        # EL. WYPŁATY and WARTOŚĆ columns
        brutto_items_dict = {}
        total_opodat_emp = 0
        total_nieopodat_emp = 0
        for _, r_wpe in emp_df.iterrows():
            wpe_val = float(r_wpe.get('WPE_Wartosc', 0) or 0)
            skrot = r_wpe.get('TWP_Skrot', 'N/A')
            if skrot not in brutto_items_dict:
                brutto_items_dict[skrot] = {'OPODAT': 0, 'NIEOPODAT': 0}
            
            if r_wpe.get('TWP_RodzajFIS', 0) == 1: # NIEOPODAT
                brutto_items_dict[skrot]['NIEOPODAT'] += wpe_val
                total_nieopodat_emp += wpe_val
            else: # OPODAT
                brutto_items_dict[skrot]['OPODAT'] += wpe_val
                total_opodat_emp += wpe_val
        
        brutto_items_list = [{'TWP_Skrot': s, 'OPODAT': v['OPODAT'], 'NIEOPODAT': v['NIEOPODAT']} for s, v in brutto_items_dict.items() if v['OPODAT'] !=0 or v['NIEOPODAT'] !=0]
        brutto_items_list.sort(key=lambda x: abs(x['OPODAT']) + abs(x['NIEOPODAT']), reverse=True)
        
        if brutto_items_list: # Add RAZEM row for EL. WYPŁATY
            brutto_items_list.append({'TWP_Skrot': '───────────────────', 'OPODAT': '─────────', 'NIEOPODAT': '─────────'}) # Visual separator
            brutto_items_list.append({'TWP_Skrot': 'RAZEM', 'OPODAT': total_opodat_emp, 'NIEOPODAT': total_nieopodat_emp})

        # PPK for employee
        ppk_p_ubezpiecz = emp_df['WPE_SklPPKPrac1'].fillna(0).astype(float).sum()
        ppk_d_ubezpiecz = emp_df['WPE_SklPPKPrac2'].fillna(0).astype(float).sum() # Assuming Prac2 is "D" for employee side
        ppk_p_platnik = emp_df['WPE_SklPPKFirma1'].fillna(0).astype(float).sum()
        ppk_d_platnik = emp_df['WPE_SklPPKFirma2'].fillna(0).astype(float).sum() # Assuming Firma2 is "D" for employer side


        # Construct rows for HTML table (max 12 rows per employee based on original example)
        emp_table_rows = []
        max_rows = max(len(brutto_items_list), 12) # Ensure enough rows for all data

        for i in range(max_rows):
            row_dict = {
                'lp': "", 'pracownik': "", 'el_wyplaty': "", 'opodat': "", 'nieopodat': "",
                'ubezpiecz': "", 'platnik': "", 'podatek': "", 'do_wyplaty': ""
            }
            if i == 0:
                row_dict['lp'] = str(employee_lp)
                row_dict['pracownik'] = str(last_name)
                row_dict['ubezpiecz'] = f"E: {format_currency(pension_contrib_emp)}"
                row_dict['platnik'] = f"E: {format_currency(pension_contrib_firm)}"
                row_dict['podatek'] = f"Zal: {format_currency(tax_advance)}"
                row_dict['do_wyplaty'] = f"KWOTA:<br>{format_currency(net_payment_ror)}" # Assuming ROR is the main payment
            elif i == 1:
                row_dict['pracownik'] = f"[{str(employee_code)}]"
                row_dict['ubezpiecz'] = f"R: {format_currency(disability_contrib_emp)}"
                row_dict['platnik'] = f"R: {format_currency(disability_contrib_firm)}"
                row_dict['podatek'] = f"Zdr: {format_currency(health_contrib)}"
            elif i == 2:
                row_dict['pracownik'] = str(first_name)
                row_dict['ubezpiecz'] = f"C: {format_currency(sickness_contrib)}"
                row_dict['platnik'] = f"W: {format_currency(accident_contrib_firm)}"
                row_dict['podatek'] = f"K: {format_currency(tax_costs)}"
            elif i == 3: # FP / Ulga
                row_dict['platnik'] = f"FP: {format_currency(fp_contrib)}"
                row_dict['podatek'] = f"Ulga: {format_currency(tax_relief)}"
            elif i == 4: # FGŚP
                row_dict['platnik'] = f"FGŚP: {format_currency(fgsp_contrib)}"
            elif i == 5: # FEP
                row_dict['platnik'] = f"FEP: {format_currency(fep_contrib)}" # This is sum of WPE_PodstFEP for employee.
            elif i == 6: # Razem ZUS
                row_dict['ubezpiecz'] = f"Razem:<br>{format_currency(total_emp_zus)}"
                row_dict['platnik'] = f"Razem:<br>{format_currency(total_firm_zus_main)}"
            elif i == 7: # Separator for PPK
                 row_dict['ubezpiecz'] = "──────────"
                 row_dict['platnik'] = "──────────"
            elif i == 8: # PPK P
                row_dict['ubezpiecz'] = f"PPK P: {format_currency(ppk_p_ubezpiecz)}"
                row_dict['platnik'] = f"PPK P: {format_currency(ppk_p_platnik)}"
            elif i == 9: # PPK D
                row_dict['ubezpiecz'] = f"PPK D: {format_currency(ppk_d_ubezpiecz)}"
                row_dict['platnik'] = f"PPK D: {format_currency(ppk_d_platnik)}"
            elif i == 10: # Razem PPK
                row_dict['ubezpiecz'] = f"Razem:<br>{format_currency(ppk_p_ubezpiecz + ppk_d_ubezpiecz)}"
                row_dict['platnik'] = f"Razem:<br>{format_currency(ppk_p_platnik + ppk_d_platnik)}"
            elif i == 11: # Gross Salary Info
                 row_dict['pracownik'] = f"PLN/mies.:<br>{format_currency(gross_salary)}"


            if i < len(brutto_items_list):
                item = brutto_items_list[i]
                # wrap_text equivalent: let CSS handle wrapping, or use <br> if specific breaks needed.
                # For TWP_Skrot, just pass the string. CSS `word-wrap: break-word;` will handle it.
                row_dict['el_wyplaty'] = str(item['TWP_Skrot']).replace("\n", "<br>")
                row_dict['opodat'] = format_currency(item['OPODAT'])
                row_dict['nieopodat'] = format_currency(item['NIEOPODAT'])

            # Replace newlines with <br> for HTML if any values are multi-line text
            for key, val in row_dict.items():
                if isinstance(val, str):
                    row_dict[key] = val.replace("\n", "<br>")
            
            emp_table_rows.append(row_dict)
        
        employees_data_for_template.append({'table_rows': emp_table_rows})

    # Summary data
    summary_calc_data = calculate_summary_data(df)
    summary_table_rows = []
    # Recreate summary table structure
    sum_ubezpiecz_total = summary_calc_data['ubezpiecz_e'] + summary_calc_data['ubezpiecz_r'] + summary_calc_data['ubezpiecz_c']
    sum_platnik_total = (summary_calc_data['platnik_e'] + summary_calc_data['platnik_r'] + summary_calc_data['platnik_w'] +
                         summary_calc_data['platnik_fp'] + summary_calc_data['platnik_fgsp'] + summary_calc_data['platnik_fep'])
    sum_do_wyplaty_total = summary_calc_data['do_wyplaty_got'] + summary_calc_data['do_wyplaty_ror']
    
    sum_ppk_ubezpiecz_total = summary_calc_data['ppk_p'] + summary_calc_data['ppk_d']
    sum_ppk_platnik_total = summary_calc_data['ppk_p_platnik'] + summary_calc_data['ppk_d_platnik']

    summary_data_map = [
        {'opodat': summary_calc_data['wartosc_opodat'], 'nieopodat': summary_calc_data['wartosc_nieopodat'],
         'ubezpiecz': f"E: {format_currency(summary_calc_data['ubezpiecz_e'])}", 'platnik': f"E: {format_currency(summary_calc_data['platnik_e'])}",
         'podatek': f"Zal: {format_currency(summary_calc_data['podatek_zal'])}", 'do_wyplaty': f"KWOTA:<br>{format_currency(sum_do_wyplaty_total)}"},
        {'ubezpiecz': f"R: {format_currency(summary_calc_data['ubezpiecz_r'])}", 'platnik': f"R: {format_currency(summary_calc_data['platnik_r'])}",
         'podatek': f"Zdr: {format_currency(summary_calc_data['podatek_zdr'])}"},
        {'ubezpiecz': f"C: {format_currency(summary_calc_data['ubezpiecz_c'])}", 'platnik': f"W: {format_currency(summary_calc_data['platnik_w'])}",
         'podatek': f"K: {format_currency(summary_calc_data['podatek_k'])}"},
        {'platnik': f"FP: {format_currency(summary_calc_data['platnik_fp'])}", 'podatek': f"Ulga: {format_currency(summary_calc_data['podatek_ulga'])}"},
        {'platnik': f"FGŚP: {format_currency(summary_calc_data['platnik_fgsp'])}"},
        {'platnik': f"FEP: {format_currency(summary_calc_data['platnik_fep'])}"}, # This uses sum of WPE_PodstFEP from all rows.
        {'ubezpiecz': f"Sum: {format_currency(sum_ubezpiecz_total)}", 'platnik': f"Sum: {format_currency(sum_platnik_total)}"},
        {'ubezpiecz': "──────────", 'platnik': "──────────"}, # Separator
        {'ubezpiecz': f"PPK P: {format_currency(summary_calc_data['ppk_p'])}", 'platnik': f"PPK P: {format_currency(summary_calc_data['ppk_p_platnik'])}"},
        {'ubezpiecz': f"PPK D: {format_currency(summary_calc_data['ppk_d'])}", 'platnik': f"PPK D: {format_currency(summary_calc_data['ppk_d_platnik'])}"},
        {'ubezpiecz': f"R: {format_currency(sum_ppk_ubezpiecz_total)}", 'platnik': f"R: {format_currency(sum_ppk_platnik_total)}"},
    ]

    for item in summary_data_map:
        row = {'c1':"", 'c2':"", 'c3':"", 'opodat': "", 'nieopodat': "", 'ubezpiecz': "", 'platnik': "", 'podatek': "", 'do_wyplaty': ""}
        for k, v in item.items():
            if k in row : row[k] = v if isinstance(v, str) else format_currency(v)
        
        for key, val in row.items(): # Ensure <br> for HTML
            if isinstance(val, str): row[key] = val.replace("\n", "<br>")
        summary_table_rows.append(row)


    legend_text = "E-Emerytalna; R-Rentowa; C-Chorobowa; W-Wypadkowa; FP-Fundusz Pracy; FGŚP-Fundusz Gwarantowanych Świadczeń Pracowniczych; FEP-Fundusz Emerytur Pomostowych; PPK P-Pracownicze Plany Kapitałowe pracownika; PPK D-PPK pracodawcy; Zal-Zaliczka na podatek; Zdr-Składka zdrowotna; K-Koszty uzyskania przychodu; Ulga-Ulga podatkowa; KWOTA-Suma wypłaty"

    template_env = Environment(loader=FileSystemLoader('.'), autoescape=select_autoescape(['html', 'xml'])) # dummy loader for string template
    template = Template(HTML_TEMPLATE_STR) # Use Template directly for string
    
    html_out = template.render(
        css_styles=CSS_STYLES_STR,
        firma_nazwa=firma_nazwa,
        firma_nip=firma_nip,
        list_number=list_number,
        period_start=period_start,
        period_end=period_end,
        payment_date=payment_date,
        lista_plac_nazwa=lista_plac_nazwa,
        employees_data=employees_data_for_template,
        summary_table_rows=summary_table_rows,
        legend_text=legend_text
    )

    # Sanitize list_number for filename
    safe_list_number = "".join(c if c.isalnum() or c in ['-', '_'] else '_' for c in str(list_number))
    output_filename = f"{output_file_base}_{safe_list_number}.pdf"
    
    try:
        HTML(string=html_out).write_pdf(output_filename) # Removed stylesheets=[CSS(string=CSS_STYLES_STR)] as it's in HTML
        print(f"Payroll PDF generated: {output_filename}")
        return output_filename
    except Exception as e:
        print(f"Error generating PDF with WeasyPrint: {e}")
        import traceback
        print(traceback.format_exc())
        # Optionally save HTML for debugging
        with open(f"{output_file_base}_{safe_list_number}_debug.html", "w", encoding="utf-8") as f_debug:
            f_debug.write(html_out)
        print(f"Debug HTML saved to: {output_file_base}_{safe_list_number}_debug.html")

        return None


def escape_filename(filename):
    illegal_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
    for char in illegal_chars:
        filename = filename.replace(char, '_')
    return filename.replace(" ", "_") # Also replace spaces

if __name__ == "__main__":
    target_db = "CDN_Nesc_sp__z_o_o_" # As per requirement
    print(f"Starting payroll PDF generation for database: {target_db}")

    # Check if DB connection variables are loaded, if not, engine might be None
    if 'engine' not in globals() or engine is None:
        print("Database engine not initialized. Exiting.")
        exit(1)

    df_data = get_payroll_data_for_db(target_db)

    if df_data.empty:
        print(f"No data retrieved for {target_db}. PDF generation skipped.")
    else:
        # Group data by list number (LPL_NumerPelny) as one LPL can result in one PDF
        grouped_by_list_num = df_data.groupby('LPL_NumerPelny')
        
        output_base_name = escape_filename(target_db)

        for list_num, df_group in grouped_by_list_num:
            print(f"\nProcessing List Number: {list_num} for {target_db}")
            generate_payroll_pdf_weasyprint(df_group, output_base_name, list_num)

    print("\nProcessing complete.")