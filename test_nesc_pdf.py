from sql_to_pdf import generate_payroll_pdf
from ConnectDB import get_all_data_no_time_filter
from pypdf import Pdf<PERSON><PERSON><PERSON>, PdfWriter
from io import BytesIO
import re
import os
import pandas as pd


def sanitize_filename(filename: str) -> str:
    """
    Sanitizes filename by removing or replacing invalid characters for Windows.

    Args:
        filename (str): Original filename

    Returns:
        str: Sanitized filename safe for Windows file system
    """
    # Replace invalid characters with underscores
    invalid_chars = r'<>:"/\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')

    # Remove any trailing dots or spaces
    filename = filename.rstrip('. ')

    return filename


def encrypt_pdf_with_nip_password(pdf_data: bytes, nip: str, output_path: str = None) -> bytes:
    """
    Encrypt PDF with NIP-based password.
    """
    if nip is None or not str(nip).strip() or len(nip) < 4:
        return None

    cleaned_nip = re.sub(r'\D', '', str(nip))
    password = cleaned_nip[-4:]

    input_stream = BytesIO(pdf_data)
    reader = PdfReader(input_stream)
    writer = PdfWriter()

    for page in reader.pages:
        writer.add_page(page)

    writer.encrypt(user_password=password, owner_password=None)

    output_stream = BytesIO()
    writer.write(output_stream)
    encrypted_pdf_data = output_stream.getvalue()

    if output_path:
        # Ensure folder exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'wb') as f:
            f.write(encrypted_pdf_data)
        print(f"Encrypted PDF saved to: {output_path}")

    print("PDF document encrypted successfully")
    return encrypted_pdf_data


def test_nesc_pdf_generation():
    """
    Test function that mimics the original PDF generation process but filters
    specifically for NESC database with invoice pattern "E/2025/05/*".
    """
    print("Starting NESC PDF generation test...")
    print("Target database: CDN_Nesc_sp__z_o_o_")
    print("Target invoice pattern: E/2025/05/*")
    print()

    try:
        # Get data from all databases without time restrictions for testing
        print("Retrieving data from all databases (no time filter)...")
        df_all = get_all_data_no_time_filter()

        if df_all.empty:
            print("No data returned from get_all_data_no_time_filter().")
            return

        print(f"Total rows retrieved from all databases: {len(df_all)}")
        print("Available databases:")
        for db in df_all['Baza'].unique():
            print(f"  - {db}")
        print()

        # Filter for NESC database
        target_database = "CDN_Nesc_sp__z_o_o_"
        df_nesc = df_all[df_all['Baza'] == target_database]

        if df_nesc.empty:
            print(f"No data found for database: {target_database}")
            print("This database might not be in the email configuration or might be offline.")
            return

        print(f"Rows found for {target_database}: {len(df_nesc)}")

        # Filter for invoice pattern E/2025/05/*
        invoice_pattern = "E/2025/05/"
        df_filtered = df_nesc[df_nesc['LPL_NumerPelny'].str.startswith(invoice_pattern, na=False)]

        if df_filtered.empty:
            print(f"No invoices found matching pattern: {invoice_pattern}*")
            print("Available invoice numbers in NESC database:")
            for invoice in df_nesc['LPL_NumerPelny'].unique():
                print(f"  - {invoice}")
            return

        print(f"Invoices matching pattern {invoice_pattern}*:")
        matching_invoices = df_filtered['LPL_NumerPelny'].unique()
        for invoice in matching_invoices:
            print(f"  - {invoice}")
        print()

        # Group by database and list number (mimicking the original process)
        grouped_data = df_filtered.groupby(['Baza', 'LPL_NumerPelny'])
        combinations = list(grouped_data.groups.keys())

        print(f"Found {len(combinations)} database/invoice combinations to process:")
        for baza, list_number in combinations:
            print(f"  - {baza}: {list_number}")
        print()

        # Create output folder
        output_folder = 'output'
        os.makedirs(output_folder, exist_ok=True)

        # Process each combination (mimicking the original PDF generation logic)
        for i, (baza, list_number) in enumerate(combinations):
            print(f"Processing combination {i + 1}/{len(combinations)}: {baza} - {list_number}")

            # Get the dataframe for this combination
            df_combo = grouped_data.get_group((baza, list_number))

            # Extract additional fields (same as original process)
            nazwa = df_combo['Nazwa'].iloc[0] if 'Nazwa' in df_combo.columns else "NESC SP. Z O.O."
            email_oka = df_combo['emailOka'].iloc[0] if 'emailOka' in df_combo.columns else "<EMAIL>"
            email_k = df_combo['emailK'].iloc[0] if 'emailK' in df_combo.columns else "<EMAIL>"
            nip = df_combo['Nip'].iloc[0] if 'Nip' in df_combo.columns else "1234567890"

            print(f"  Company: {nazwa}")
            print(f"  NIP: {nip}")
            print(f"  Rows in this combination: {len(df_combo)}")

            try:
                print(f"  Generating PDF for {baza} - {list_number}...")
                
                # Generate PDF using the same function as the original process
                pdf_data = generate_payroll_pdf(df_combo, list_number=list_number, return_data=True)
                
                if pdf_data:
                    print(f"  PDF generated successfully ({len(pdf_data)} bytes)")
                    
                    # Create output filename
                    output_filename = f"nesc_test_{list_number}.pdf"
                    output_filename = sanitize_filename(output_filename)
                    output_path = os.path.join(output_folder, output_filename)
                    
                    # Encrypt and save PDF (same as original process)
                    encrypt_pdf_with_nip_password(pdf_data=pdf_data, nip=nip, output_path=output_path)
                    print(f"  Saved as: {output_filename}")
                    
                else:
                    print(f"  Failed to generate PDF for {baza} - {list_number}")
                    
            except Exception as e:
                print(f"  Error generating PDF for {baza} - {list_number}: {e}")
                import traceback
                print(f"  Detailed error: {traceback.format_exc()}")
            
            print()  # Add spacing between combinations

        print("NESC PDF generation test completed!")

    except Exception as e:
        print(f"Error in test process: {e}")
        import traceback
        print(f"Detailed error: {traceback.format_exc()}")


if __name__ == "__main__":
    test_nesc_pdf_generation()
