import os
from dotenv import load_dotenv
import ConnectDB
import requests
import pandas as pd

load_dotenv()

def send_sms():
    df_query = ConnectDB.query_execute()
    if df_query.empty:
        pass
    else:

        print(df_query)
        id_list = []
        for index, row in df_query.iterrows():
            nr_tel = row['numerTel']
            body = row['tresc']

            if not nr_tel or pd.isna(nr_tel) or str(nr_tel).strip() == "":
                print(f"Pominięto rekord z pustym numerem telefonu (ID: {row['ID']})")
                continue
            # wysyłka sms w tym miejscu
            url = "https://api.hostedsms.pl/SimpleApi/sendsms"
            payload = {
                "UserEmail": os.getenv("SMS_USERNAME"),
                "Password": os.getenv("SMS_PASSWORD"),
                "Sender": os.getenv("SMS_SENDER"),
                "Phone": nr_tel,
                "Message": body,
                "ConvertMessageToGSM7" : True
            }


            headers = {
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                "Accept": "application/json"
            }

            response = requests.post(url, data=payload, headers=headers)

            if response.status_code == 200:
                result = response.json()
                if "MessageId" in result:
                    print("Wiadomość wysłana. ID:", result["MessageId"])
                elif "ErrorMessage" in result:
                    print("Błąd:", result["ErrorMessage"])
                else:
                    print("Nieoczekiwana odpowiedź:", result)
            else:
                print("Błąd HTTP:", response.status_code, response.text)
            id_list.append(row['ID'])
        return id_list


