from apscheduler.triggers.interval import IntervalTrigger
from plombery import task, get_logger, Trigger, register_pipeline
import ConnectDB
import sendEmail

@task
def execute_SMS():
    ConnectDB.insert()

@task
def execute_email():
    sendEmail.main()

register_pipeline(
    id="mail_i_sms",
    description="Wysyłka powiadomień SMS dotyczących deklaracji do klientów oraz wysyłka mailowa plików z listami płac do klientów",
    tasks=[execute_SMS,execute_email],
    triggers=[
        Trigger(
            id="4 hours",
            name="4 hours",
            description="Run the pipeline every 4 hours",
            schedule=IntervalTrigger(hours=4),
        ),
    ],
)

if __name__ == "__main__":
    import uvicorn

    uvicorn.run("plombery:get_app", reload=True, factory=True)