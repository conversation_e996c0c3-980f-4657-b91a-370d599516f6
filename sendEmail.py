from sql_to_pdf import process_all_databases_and_return_data
from pypdf import PdfReader, PdfWriter
from io import BytesIO
import re
import smtplib
from email.message import EmailMessage
import os

def download_pdf(pdf_data: bytes, output_path: str):
    """
    Zapisuje plik PDF na dysku.

    Args:
        pdf_data (bytes): Dane binarne pliku PDF
        output_path (str): Ścieżka do zapisu (np. 'C:/ścieżka/nazwa_pliku.pdf')
    """
    try:
        with open(output_path, 'wb') as f:
            f.write(pdf_data)
        print(f"PDF zapisany w: {output_path}")
    except Exception as e:
        print(f"Błąd zapisu PDF: {e}")

def send_email_on_error():
    body = f"brak nipu"

    msg = EmailMessage()
    msg['Subject'] = 'Błąd przy wysyłce - listy płac'
    msg['From'] = "<EMAIL>"
    msg['To'] = "<EMAIL>"
    msg.set_content(body)


    try:
        with smtplib.SMTP('smtp.dpoczta.pl', 587) as smtp:
            smtp.starttls()
            smtp.login("<EMAIL>", "%pxQ3=Ui6J")
            smtp.send_message(msg)
            print(f"Wysłano wiadomosc o bledzie")
    except Exception as e:
        print(f"Błąd podczas wysyłki maila o bledzie")
def send_payroll_email(recipients: str, dw: str, nazwa: str, pdf_attachments: list):
    """
    Send an email with multiple PDF attachments for the same database.

    Args:
        recipients (str): Email recipients
        dw (str): CC recipients
        nazwa (str): Company name
        pdf_attachments (list): List of dictionaries with keys 'list_number', 'pdf_data'
    """
    sender_email = "<EMAIL>"
    bcc_email = "<EMAIL>"

    # Create a list of document numbers for the email body
    list_numbers = [attachment['list_number'] for attachment in pdf_attachments]
    list_numbers_text = ", ".join(list_numbers)

    subject = f"Listy płac – {nazwa}"

    body = f"""Dzień dobry,

W załączniku przesyłamy listy płac. Numery dokumentów: {list_numbers_text}.

Z wyrazami szacunku,
Zespół TaxCoach
"""

    msg = EmailMessage()
    msg['From'] = sender_email
    msg['To'] = recipients
    msg['Cc'] = dw
    msg['Bcc'] = bcc_email
    msg['Subject'] = subject
    msg.set_content(body)

    # Add all PDF attachments
    for attachment in pdf_attachments:
        list_number = attachment['list_number']
        pdf_data = attachment['pdf_data']

        filename = f"{nazwa}_{list_number.replace('/', '_')}.pdf"
        msg.add_attachment(pdf_data, maintype='application', subtype='pdf', filename=filename)

    try:
        with smtplib.SMTP('smtp.dpoczta.pl', 587) as smtp:
            smtp.starttls()
            smtp.login("<EMAIL>", "%pxQ3=Ui6J")
            smtp.send_message(msg)
            print(f"Email sent successfully with {len(pdf_attachments)} attachments for {nazwa}")
    except Exception as e:
        print(f"Błąd podczas wysyłki maila: {e}")


def encrypt_pdf_with_nip_password(pdf_data: bytes, nip: str) -> bytes:
    if nip is None or not str(nip).strip() or len(nip) < 4:
        send_email_on_error()
        return None

    cleaned_nip = re.sub(r'\D', '', str(nip))


    password = cleaned_nip[-4:]


    input_stream = BytesIO(pdf_data)
    reader = PdfReader(input_stream)
    writer = PdfWriter()

    for page in reader.pages:
        writer.add_page(page)


    writer.encrypt(user_password=password, owner_password=None)


    output_stream = BytesIO()
    writer.write(output_stream)
    print(f"Dokument pdf zaszyfrowany poprawnie")

    return output_stream.getvalue()

def main():
    """
    Main function to process all databases and send emails with PDFs grouped by database.
    """
    pdf_data_list = process_all_databases_and_return_data()

    if not pdf_data_list:
        print("Brak danych do przetworzenia.")
        return

    # Group PDFs by database
    databases = {}
    for entry in pdf_data_list:
        baza = entry['baza']
        list_number = entry['list_number']
        pdf_data = entry['pdf_data']
        nazwa = entry['nazwa']
        nip = entry['nip']
        email_oka = entry['emailOka'] if 'emailOka' in entry and entry['emailOka'] and str(entry['emailOka']).strip() != "" else "<EMAIL>"
        email_k = entry['emailK'] if 'emailK' in entry and entry['emailK'] and str(entry['emailK']).strip() != "" else "<EMAIL>"
        print(f"Zaczynam szyfrowanie pdf dla bazy: {baza} i nipu {nip}")
        # Encrypt the PDF
        pdf_encrypted = encrypt_pdf_with_nip_password(pdf_data=pdf_data, nip=nip)

        if pdf_encrypted is not None:
        # Create a new entry with encrypted PDF
            pdf_entry = {
                'list_number': list_number,
                'pdf_data': pdf_encrypted
            }

            # Add to the database group
            if baza not in databases:
                databases[baza] = {
                    'nazwa': nazwa,
                    'attachments': [],
                    'recipients': email_k,  # Default recipient
                    'dw': email_oka  # Default CC
                }

            databases[baza]['attachments'].append(pdf_entry)

    # Send one email per database with all PDFs
    for baza, db_info in databases.items():
        nazwa = db_info['nazwa']
        attachments = db_info['attachments']
        recipients = db_info['recipients']
        dw = db_info['dw']

        print(f"Sending email for database {baza} with {len(attachments)} PDFs")
        send_payroll_email(
            recipients=recipients,
            # recipients= '<EMAIL>',
            dw=dw,
            # dw='<EMAIL>',
            nazwa=nazwa,
            pdf_attachments=attachments
        )

