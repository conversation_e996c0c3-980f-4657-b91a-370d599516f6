{"level": "INFO", "message": "Executing pipeline `mail_i_sms` #15 via trigger `_manual`", "loggerName": "plombery.15", "timestamp": "2025-05-20T12:25:10.599Z", "pipeline": "mail_i_sms", "task": null}
{"level": "INFO", "message": "Executing task execute_SMS", "loggerName": "plombery.15", "timestamp": "2025-05-20T12:25:10.602Z", "pipeline": "mail_i_sms", "task": null}
{"level": "ERROR", "message": "", "loggerName": "plombery.15", "timestamp": "2025-05-20T12:25:10.606Z", "pipeline": "mail_i_sms", "task": null, "exc_info": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\PycharmProjects\\pythonProject9\\.venv\\Lib\\site-packages\\plombery\\orchestrator\\executor.py\", line 129, in run\n    flowing_data = await _execute_task(task, flowing_data, pipeline_params)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\PycharmProjects\\pythonProject9\\.venv\\Lib\\site-packages\\plombery\\orchestrator\\executor.py\", line 195, in _execute_task\n    result = await task.run(*args, **kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\PycharmProjects\\pythonProject9\\.venv\\Lib\\site-packages\\plombery\\pipeline\\__init__.py\", line 22, in wrapper_decorator\n    value = await asyncio.to_thread(func, *args, **kwargs)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\asyncio\\threads.py\", line 25, in to_thread\n    return await loop.run_in_executor(None, func_call)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python311\\Lib\\concurrent\\futures\\thread.py\", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\PycharmProjects\\pythonProject9\\app.py\", line 8, in execute_SMS\n    raise ValueError\nValueError"}
